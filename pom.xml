<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip.corp.bff.service</groupId>
        <artifactId>template-parent-pom</artifactId>
        <version>1.0.28</version>
    </parent>
    <groupId>com.ctrip.corp.bff.hotel.service</groupId>
    <artifactId>com-bff-hotel-book-service</artifactId>
    <packaging>war</packaging>
    <version>1.0.0</version>
    <properties>
        <java.version>21</java.version>
        <jacoco-maven-plugin.version>0.8.11</jacoco-maven-plugin.version>
        <jacoco.report.path>${project.build.directory}/jacoco.exec</jacoco.report.path>
        <sonar.jacoco.reportPaths>${project.build.directory}/jacoco.exec</sonar.jacoco.reportPaths>
        <baiji.service.define>com.ctrip.corp.bff.hotel.book.contract.CorpBffHotelBookService</baiji.service.define>
        <client.version>0.0.64</client.version>
        <ibu-shark-sdk.version>5.1.3</ibu-shark-sdk.version>
        <jacoco.version>0.8.11</jacoco.version>
        <jmockit.version>1.52.0-jdk21</jmockit.version>
        <framework-hotel-common.version>1.1.76</framework-hotel-common.version>
        <file_encoding>UTF-8</file_encoding>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.4.15</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.6</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.71</version>
            </dependency>
            <dependency>
                <artifactId>basic-integration-entity</artifactId>
                <groupId>com.ctrip.corp.bff.service</groupId>
                <version>0.0.6</version>
            </dependency>
            <!-- 时区组件,公共组包版本过低，先强指 -->
            <dependency>
                <groupId>com.ctrip.corp.foundation</groupId>
                <artifactId>corp-timezone</artifactId>
                <version>1.0.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!--spring依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>1.4.3.RELEASE</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <!--tomcat-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>${jmockit.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- contract -->
        <dependency>
            <groupId>com.ctrip.model.bff.hotel</groupId>
            <artifactId>corpbffhotelbookservice</artifactId>
            <version>${client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>aopalliance</groupId>
                    <artifactId>aopalliance</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.bff.framework</groupId>
            <artifactId>corp-bff-framework-hotel-resourcetoken</artifactId>
            <version>0.0.54</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.bff.hotel.service</groupId>
            <artifactId>framework-hotel-common</artifactId>
            <version>${framework-hotel-common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.hotel.service</groupId>
                    <artifactId>framework-hotel-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jmockit</artifactId>
                    <groupId>org.jmockit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--酒店agg公共服务-->
        <dependency>
            <groupId>com.ctrip.soa.corp.booking.corphotelbookcommonws.v1</groupId>
            <artifactId>corphotelbook-commonws</artifactId>
            <version>1.4.56</version>
        </dependency>
        <!--AGG城市查询接口-->
        <dependency>
            <groupId>com.ctrip.corp.agg.base</groupId>
            <artifactId>basedataservice</artifactId>
            <version>1.1.22</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.user.userinfoqueryservice.v1</groupId>
            <artifactId>userinfoqueryservice</artifactId>
            <version>0.0.15</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.setting.corpconfigurationservice.v1</groupId>
            <artifactId>corpconfigurationservice</artifactId>
            <version>0.8.4</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.booking.preparews</groupId>
            <artifactId>corpbookingprepareservice</artifactId>
            <version>0.0.16</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--agg可定检查相关服务-->
        <dependency>
            <groupId>com.ctrip.soa.corp.booking.corphotelaggroomavailservice.v1</groupId>
            <artifactId>corphotelaggroomavailservice</artifactId>
            <version>0.5.67</version>
        </dependency>
        <!--BFF基础工具服务校验敏感信息-->
        <dependency>
            <groupId>com.ctrip.corp.31804</groupId>
            <artifactId>corpbfftools</artifactId>
            <version>0.0.35</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>basic-integration-entity</artifactId>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.bff.specific</groupId>
            <artifactId>corpbffspecificservice</artifactId>
            <version>1.0.66</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 订单详情服务(新) -->
        <dependency>
            <groupId>com.ctrip.soa.22608</groupId>
            <artifactId>orderdataaggregationqueryservice</artifactId>
            <version>2.9.27</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1</groupId>
            <artifactId>corphotelorderdetailservice</artifactId>
            <version>0.10.27</version>
        </dependency>
        <!--mice服务-->
        <dependency>
            <groupId>com.ctrip.corp.bff.mice.basic.auth</groupId>
            <artifactId>CorpBffMiceBasicAuthService</artifactId>
            <version>0.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--审批流服务-->
        <dependency>
            <groupId>com.ctrip.soa.corp.authorize.approvews.v1</groupId>
            <artifactId>corpapproval4jservice</artifactId>
            <version>1.23.6</version>
        </dependency>
        <!--创单接口-->
        <dependency>
            <groupId>com.ctrip.soa.corp.booking.corphotelbookservice.v1</groupId>
            <artifactId>corphotelbookservice</artifactId>
            <version>********</version>
        </dependency>
        <!--支付-->
        <dependency>
            <groupId>com.ctrip.corp.bff.payment</groupId>
            <artifactId>corpbffpaymentservice</artifactId>
            <version>0.0.13</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--save common data-->
        <dependency>
            <groupId>com.ctrip.soa.20183</groupId>
            <artifactId>orderfoundationcenterdatasyncservice</artifactId>
            <version>0.5.3</version>
        </dependency>
        <!--沿用授权-->
        <dependency>
            <groupId>com.ctrip.soa.20184</groupId>
            <artifactId>orderfoundationcenterauthorizationservice</artifactId>
            <version>0.2.8</version>
        </dependency>
        <!--创建行程-->
        <dependency>
            <groupId>com.ctrip.soa.21234</groupId>
            <artifactId>triporderservice</artifactId>
            <version>0.2.30</version>
        </dependency>
        <!--单点登录-->
        <dependency>
            <groupId>com.ctrip.corp.bff.profile</groupId>
            <artifactId>corpbffprofileservice</artifactId>
            <version>0.0.26</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>basic-integration-entity</artifactId>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--丰享支付-->
        <dependency>
            <groupId>com.ctrip.soa.21685</groupId>
            <artifactId>orderpaymentcentertransactionservice</artifactId>
            <version>1.0.18</version>
        </dependency>
        <!--商户号-->
        <dependency>
            <groupId>com.ctrip.corp.order</groupId>
            <artifactId>payment-center-bill-contract</artifactId>
            <version>1.2.32</version>
        </dependency>
        <!--用户历史发票-->
        <dependency>
            <groupId>com.ctrip.soa.corp.user.corpinfoqueryws.v1</groupId>
            <artifactId>corpinfoqueryws</artifactId>
            <version>0.0.14</version>
        </dependency>
        <!--个人账户信息-->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>2.3-groovy-4.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.bff.service</groupId>
            <artifactId>framework-specific</artifactId>
            <version>1.1.9</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish</groupId>
                    <artifactId>javax.xml.rpc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun</groupId>
                    <artifactId>tools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.soa.caravan</groupId>
                    <artifactId>caravan</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.soa.20183</groupId>
                    <artifactId>orderfoundationcenterdatasyncservice</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.agg.base</groupId>
                    <artifactId>basedataservice</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>integration-entity</artifactId>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ibu-gdpr-sdk</artifactId>
                    <groupId>com.ctrip.ibu.platform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kms-sdk</artifactId>
                    <groupId>com.ctrip.infosec.kms</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--- 定义 jacoco 依赖  注意不是在dependencyManagement， dependencyManagement中只是声明-->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <version>${jacoco.version}</version>
            <classifier>runtime</classifier>
        </dependency>
        <!--tmc-client管控服务版本以common包引入的版本为准，因为差标接口是统一wrapper-->
        <!--酒店服务费-->
        <dependency>
            <groupId>com.ctrip.corp.agg.hotel.salestrategyservice</groupId>
            <artifactId>salestrategy-client</artifactId>
            <version>0.3.17</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.22462</groupId>
            <artifactId>orderapprovalservice</artifactId>
            <version>0.1.21</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.agg.hotel.calculationservice</groupId>
            <artifactId>hotelagg-calculation-service</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.order</groupId>
            <artifactId>reimbursement-service-contract</artifactId>
            <version>1.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.user.corpuserinfo4j.v1</groupId>
            <artifactId>corpuserinfoservice4j</artifactId>
            <version>0.0.331</version>
        </dependency>
        <!--IBU优惠券平台-->
        <dependency>
            <groupId>com.ctrip.ibu.member</groupId>
            <artifactId>member-coupon-platform-client</artifactId>
            <version>1.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.hotel.order</groupId>
            <artifactId>reward-contract</artifactId>
            <version>1.5.3</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.20202</groupId>
            <artifactId>ordermanagementcenterhotelservice</artifactId>
            <version>1.0.60</version>
        </dependency>
        <!--默认发票信息查询-->
        <dependency>
            <groupId>com.ctrip.basebiz.invoice</groupId>
            <artifactId>invoiceservice</artifactId>
            <version>1.1.16</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.platform.members.geolocation</groupId>
            <artifactId>geolocationservice</artifactId>
            <version>1.1.40</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.model.agg.hotel</groupId>
            <artifactId>hotel-member-service-contract-client</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.order</groupId>
            <artifactId>corporder-common</artifactId>
            <version>1.5.23</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.flight.intl.engine</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.foundation</groupId>
                    <artifactId>common-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.foundation</groupId>
                    <artifactId>common-plus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.foundation</groupId>
                    <artifactId>corp-log</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.foundation</groupId>
                    <artifactId>corp-dal</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.objenesis</groupId>
                    <artifactId>objenesis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--支持QPM指标-->
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-prometheus-exporter</artifactId>
            <version>3.6.13-Java21</version>
        </dependency>
        <!--行程信息同步-->
        <dependency>
            <groupId>com.ctrip.corp.pub</groupId>
            <artifactId>endorsement-contract</artifactId>
            <version>0.1.5</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--政策执行人关联-->
        <dependency>
            <groupId>com.ctrip.soa.corp.user.dbdecoupling.v1</groupId>
            <artifactId>dbdecouplingservice</artifactId>
            <version>0.0.18</version>
        </dependency>
        <!--Offline绑定信息-->
        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>basebiz.inboundservice.ws.client</artifactId>
            <version>1.0.9</version>
        </dependency>
        <!--散客出行人信息 非员工-->
        <dependency>
            <groupId>com.ctrip.soa.platform.members.bbzmbrcommonpassenger.v1</groupId>
            <artifactId>bbzmbrcommonpassenger</artifactId>
            <version>1.0.41</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.user.group4j.v1</groupId>
            <artifactId>group4j</artifactId>
            <version>0.0.66</version>
        </dependency>
        <!--保存订单成本中心-->
        <dependency>
            <groupId>com.ctrip.soa.20185</groupId>
            <artifactId>orderfoundationcentercostinfoservice</artifactId>
            <version>0.1.0</version>
        </dependency>
        <!--获取成本中心配置-->
        <dependency>
            <groupId>com.ctrip.soa.corp.user.corpcostcenter.v1</groupId>
            <artifactId>corpcostcenterservice</artifactId>
            <version>0.0.27</version>
        </dependency>
        <!-- 创单校验服务 -->
        <dependency>
            <groupId>com.ctrip.soa.24373</groupId>
            <artifactId>CorpOrderVerifyService</artifactId>
            <version>0.0.5</version>
        </dependency>
        <!--发票信息-->
        <dependency>
            <groupId>com.ctrip.soa.corp.settlement.invoice.v1</groupId>
            <artifactId>corpsettlementinvoiceservice</artifactId>
            <version>2.9.38</version>
        </dependency>
        <!--礼品卡服务接口-->
        <dependency>
            <groupId>com.ctrip.soa.platform.lipin.giftcardpayws.v1</groupId>
            <artifactId>giftcardpayws</artifactId>
            <version>2.0.7</version>
        </dependency>
        <!--时区组件-->
        <dependency>
            <groupId>com.ctrip.corp.foundation</groupId>
            <artifactId>corp-timezone</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.hotel.groupvippackageFunction</groupId>
            <artifactId>htlgetgroupvippackagefunction</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!--腾讯员工号校验所在服务-->
        <dependency>
            <groupId>com.ctrip.soa.corp.booking.corpcustomrequirementservice.v1</groupId>
            <artifactId>corpcustomrequirementservice</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!--查询单号是订单or行程号-->
        <dependency>
            <groupId>com.ctrip.soa.21759</groupId>
            <artifactId>orderindexextservice</artifactId>
            <version>0.5.8</version>
        </dependency>
        <!-- 管理员权限查询 -->
        <dependency>
            <groupId>com.ctrip.soa.23144</groupId>
            <artifactId>corpauthoritymanageservice</artifactId>
            <version>0.0.26</version>
        </dependency>
        <!--账户出行人信息 员工-->
        <dependency>
            <groupId>com.ctrip.soa.corp.user.appmanagerservice.v1</groupId>
            <artifactId>appmanagerservice</artifactId>
            <version>0.0.42</version>
        </dependency>
    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                    </excludes>
                    <append>false</append>
                </configuration>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>${jacoco.report.path}</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <forkCount>8</forkCount>
                    <threadCount>8</threadCount>
                    <reuseForks>true</reuseForks>
                    <argLine>
                        -Xmx1024m
                        -Dfile.encoding=UTF-8
                        -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
                    </argLine>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>${jacoco.report.path}</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <!--groovy 编译-->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <encoding>${file_encoding}</encoding>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources/</generatedSourcesDirectory>
                    <annotationProcessors>
                        <annotationProcessor>
                            com.ctrip.corp.bff.framework.template.service.generate.ServiceGenerateAnnotationProcessor
                        </annotationProcessor>
                    </annotationProcessors>

                    <showWarnings>true</showWarnings>
                    <fork>true</fork>
                    <compilerArgs>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
                        <arg>-ABaijiServiceDefine=${baiji.service.define}</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>