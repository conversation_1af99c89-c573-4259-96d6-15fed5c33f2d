package com.ctrip.corp.bff.hotel.book.common.mapper;

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount;
import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.AmountInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.ApprovalBillInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.BaseInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.BookInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CorpXProductGuestInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CorpXProductInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CostCenterInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.GuestInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.IntlServiceChargeInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.RoomDailyPriceInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.ServiceChargeInfoType;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.FlashStayInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ServiceChargeResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.AllRoomTypeEnum;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.ApprovalNoInfoType;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputItem;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputVo;
import com.ctrip.corp.bff.framework.specific.common.utils.SaveCommonDataCostCenterInfoTypeUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.*;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelGuaranteeTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckTravelPolicy;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.ctrip.soa._20183.PassengerCostCenterInfoType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfCheckTravelPolicyRequestType
        extends AbstractMapper<Tuple1<WrapperOfCheckTravelPolicy>, CheckTravelPolicyRequestType> {
    // 前收服务费
    private static final String CHARGE_MOMENT_PRE = "PreCharge";
    private static final String ACCOUNT_PAY = "AccountPay";
    private static final String PERSONAL_PAY_SERVICE_CHARGE = "PERSONAL_PAY_SERVICE_CHARGE";
    private static final String CORP_PAY_SERVICE_CHARGE = "CORP_PAY_SERVICE_CHARGE";
    private static final String UNKNOWN = "UNKNOWN";
    public static final String CORP_PAY = "CORP_PAY";
    public static final String PERSONAL_PAY = "PERSONAL_PAY";
    public static final String MIX_PAY = "MIX_PAY";
    public static final String PERSON = "PERSON";
    public static final String ACCOUNT_GUARANTEE = "ACCOUNT_GUARANTEE";
    public static final String INDIVIDUAL_GUARANTEE = "INDIVIDUAL_GUARANTEE";
    public static final String NONE = "NONE";
    public static final String ONLINE = "ONLINE";
    public static final String OFFLINE = "OFFLINE";
    public static final String APP = "APP";
    // 支付方式场景来源-创单环节来源入参
    public static final String SCENE_PAY_TYPE_FROM_INPUT = "PAY_TYPE_FROM_INPUT";
    // 支付方式场景来源-填写页bookinit来源前端与agg返回的支付方式综合后的默支付方式
    public static final String SCENE_PAY_TYPE_FROM_DEFAULT = "SCENE_PAY_TYPE_FROM_DEFAULT";
    public static final int RC_APPROVAL_CONTROL = 2;
    public static final int RC_CONTROL = 1;
    public static final String OVER_STANDARD = "OVER_STANDARD";
    public static final String AGREEMENT = "AGREEMENT";
    public static final String BOOK_AHEAD = "BOOK_AHEAD";
    public static final String CONFLICT_BOOK = "CONFLICT_BOOK";
    private final String COST_CENTER1 = "CostCenter1";
    private final String COST_CENTER2 = "CostCenter2";
    private final String COST_CENTER3 = "CostCenter3";
    private final String COST_CENTER4 = "CostCenter4";
    private final String COST_CENTER5 = "CostCenter5";
    private final String COST_CENTER6 = "CostCenter6";
    private static final int COSTCENTER_LEVEL_1 = 1;
    private static final int COSTCENTER_LEVEL_2 = 2;
    private static final int COSTCENTER_LEVEL_3 = 3;
    private static final int COSTCENTER_LEVEL_4 = 4;
    private static final int COSTCENTER_LEVEL_5 = 5;
    private static final int COSTCENTER_LEVEL_6 = 6;


    @Override
    protected CheckTravelPolicyRequestType convert(Tuple1<WrapperOfCheckTravelPolicy> typeTuple) {
        WrapperOfCheckTravelPolicy checkTravelPolicy = typeTuple.getT1();
        ResourceToken resourceToken = checkTravelPolicy.getResourceToken();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = checkTravelPolicy.getCheckAvailInfo();
        WrapperOfAccount.AccountInfo accountInfo = checkTravelPolicy.getAccountInfo();
        HotelPolicyInput hotelPolicyInput = checkTravelPolicy.getHotelPolicyInput();
        IntegrationSoaRequestType integrationSoaRequestType = checkTravelPolicy.getIntegrationSoaRequestType();
        ApprovalInput approvalInput = checkTravelPolicy.getApprovalInput();
        AddPriceInput addPriceInput = checkTravelPolicy.getAddPriceInput();
        HotelBookInput hotelBookInput = checkTravelPolicy.getHotelBookInput();
        List<StrategyInfo> strategyInfos = checkTravelPolicy.getStrategyInfos();
        String scene = checkTravelPolicy.getScene();
        String costCenterStr = checkTravelPolicy.getCostCenterStr();
        HotelInsuranceInput hotelInsuranceInput = checkTravelPolicy.getHotelInsuranceInput();
        List<HotelPayTypeInput> hotelPayTypeInputs = checkTravelPolicy.getHotelPayTypeInputs();
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType =
                checkTravelPolicy.getGetSupportedPaymentMethodResponseType();
        FlashStayInput flashStayInput = checkTravelPolicy.getFlashStayInput();
        Map<String, StrategyInfo> strategyInfoMap = checkTravelPolicy.getStrategyInfoMap();
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType =
                checkTravelPolicy.getCalculateServiceChargeV2ResponseType();
        List<RCInput> rcInfos = checkTravelPolicy.getRcInfos();
        AllocationResultToken allocationResultToken = checkTravelPolicy.getAllocationResultToken();
        List<HotelBookPassengerInput> hotelBookPassengerInputs = checkTravelPolicy.getHotelBookPassengerInputs();
        CityInput cityInput = checkTravelPolicy.getCityInput();
        CostCenterInfo costCenterInfo = checkTravelPolicy.getCostCenterInfo();
        if (cityInput == null) {
            cityInput = new CityInput();
            cityInput.setCityId(Optional.ofNullable(resourceToken.getHotelResourceToken())
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null));
        }
        CorpPayInfo corpPayInfo = checkTravelPolicy.getCorpPayInfo();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig =
            checkTravelPolicy.getQconfigOfCertificateInitConfig();
        CheckTravelPolicyRequestType checkTravelPolicyRequestType = new CheckTravelPolicyRequestType();
        checkTravelPolicyRequestType.setTravelPolicyToken(
            StringUtil.isNotBlank(checkTravelPolicy.getPolicyToken()) ? checkTravelPolicy.getPolicyToken() :
                resourceToken.getReservationResourceToken().getPolicyToken());
        checkTravelPolicyRequestType.setAmountInfo(
            getAmountInfoType(checkAvailInfo, addPriceInput, hotelBookInput, calculateServiceChargeV2ResponseType,
                scene, hotelPayTypeInputs, resourceToken, checkTravelPolicy.getRoomPayType(), strategyInfos,
                checkTravelPolicy.getStrategyInfoMap(), checkTravelPolicy.getServicePayType(),
                integrationSoaRequestType));
        checkTravelPolicyRequestType.setBookInfo(
            getBookInfoType(hotelInsuranceInput, hotelPayTypeInputs, getSupportedPaymentMethodResponseType,
                flashStayInput, accountInfo, calculateServiceChargeV2ResponseType, rcInfos, scene, resourceToken,
                approvalInput, checkTravelPolicy.getRoomPayType(), strategyInfos, corpPayInfo, integrationSoaRequestType));
        checkTravelPolicyRequestType.setApprovalBillInfo(
            getApprovalBillInfo(approvalInput, accountInfo, checkAvailInfo, allocationResultToken,
                hotelBookPassengerInputs, cityInput, corpPayInfo, costCenterStr, scene, strategyInfos,
                qconfigOfCertificateInitConfig, integrationSoaRequestType, strategyInfoMap, costCenterInfo));
        checkTravelPolicyRequestType.setTraceId(integrationSoaRequestType.getRequestId());
        checkTravelPolicyRequestType.setWsId(checkAvailInfo.getWsId());
        checkTravelPolicyRequestType.setBaseInfo(
            getBaseInfoType(integrationSoaRequestType, hotelPolicyInput, scene, accountInfo));
        return checkTravelPolicyRequestType;
    }

    protected ApprovalBillInfoType getApprovalBillInfo(ApprovalInput approvalInput,
        WrapperOfAccount.AccountInfo accountInfo, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        AllocationResultToken allocationResultToken, List<HotelBookPassengerInput> hotelBookPassengerInputs,
        CityInput cityInput, CorpPayInfo corpPayInfo, String costCenterStr, String scene,
        List<StrategyInfo> strategyInfos, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        IntegrationSoaRequestType integrationSoaRequestType, Map<String, StrategyInfo> strategyInfoMap,
        CostCenterInfo costCenterInfo) {
        // 降噪-这玩意传入的乱七八糟的，都没人敢动了 todo：能否抹平差异参考后续结论  http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
        if (StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfos)) {
            if (integrationSoaRequestType.getUserInfo().getPos() != PosEnum.CHINA) {
                return null;
            }
            if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(cityInput.getCityId()), corpPayInfo)) {
                ApprovalBillInfoType approvalBillInfoType = new ApprovalBillInfoType();
                approvalBillInfoType.setPreApprovalNumber(
                    getSubApprovalNo(approvalInput, cityInput, corpPayInfo, accountInfo));
                return approvalBillInfoType;
            }
            return null;
        }
        ApprovalBillInfoType approvalBillInfoType = new ApprovalBillInfoType();
        approvalBillInfoType.setPreApprovalNumber(getSubApprovalNo(approvalInput, cityInput, corpPayInfo,
                accountInfo));
        if (SCENE_PAY_TYPE_FROM_INPUT.equalsIgnoreCase(scene)) {
            approvalBillInfoType.setGuests(Optional.ofNullable(hotelBookPassengerInputs).orElse(new ArrayList<>()).stream().map(passenger -> {
                GuestInfoType guestInfoType = new GuestInfoType();
                // 出行人uid
                guestInfoType.setUid(
                    BooleanUtil.parseStr(true).equalsIgnoreCase(passenger.getHotelPassengerInput().getEmployee()) ?
                        passenger.getHotelPassengerInput().getUid() : null);
                // 出行人姓名
                guestInfoType.setName(
                    OrderCreateProcessorOfUtil.getUseName(passenger, cityInput.getCityId(), checkAvailInfo,
                        qconfigOfCertificateInitConfig, strategyInfoMap));
                guestInfoType.setInfoId(passenger.getHotelPassengerInput().getInfoId());
                if (allocationResultToken != null) {
                    guestInfoType.setAccountDistributionAmount(
                        buildShareAmount(allocationResultToken.getSettleAllocationAmount(),
                            StringUtil.isNotBlank(passenger.getHotelPassengerInput().getUid()) ?
                                passenger.getHotelPassengerInput().getUid() :
                                passenger.getHotelPassengerInput().getInfoId(),
                            passenger.getHotelPassengerInput().getTemporaryId()));
                }
                if (SCENE_PAY_TYPE_FROM_INPUT.equalsIgnoreCase(scene)) {
                    if (costCenterInfo != null && StringUtils.isNotBlank(costCenterInfo.getCostCenterToken())) {
                        guestInfoType.setCostCenterInfoList(
                            buildCostCenterInfoListNew(costCenterInfo, hotelBookPassengerInputs, cityInput,
                                qconfigOfCertificateInitConfig, strategyInfoMap, checkAvailInfo, passenger));
                    } else {
                        guestInfoType.setCostCenterInfoList(buildCostCenterInfoList(costCenterStr, passenger));
                    }
                }
                return guestInfoType;
            }).collect(Collectors.toList()));
        } else {
            return StringUtil.isEmpty(approvalBillInfoType.getPreApprovalNumber()) ? null : approvalBillInfoType;
        }
        return approvalBillInfoType;
    }

    protected boolean samePsg(PassengerCostCenterInfoType passengerCostCenterInfoType,
        HotelBookPassengerInput passenger) {
        if (passengerCostCenterInfoType == null || passenger == null) {
            return false;
        }
        if (StringUtil.isNotBlank(passengerCostCenterInfoType.getPassengerUid()) && StringUtil.equalsIgnoreCase(
            passengerCostCenterInfoType.getPassengerUid(), passenger.getHotelPassengerInput().getUid())) {
            return true;
        }
        if (StringUtil.isNotBlank(passengerCostCenterInfoType.getInfoId()) && StringUtil.equalsIgnoreCase(
            passengerCostCenterInfoType.getInfoId(), passenger.getHotelPassengerInput().getInfoId())) {
            return true;
        }
        return false;
    }

    protected String buildCostCenterType(Integer level) {
        if (level == null) {
            return null;
        }
        switch (level) {
            case COSTCENTER_LEVEL_1:
                return COST_CENTER1;
            case COSTCENTER_LEVEL_2:
                return COST_CENTER2;
            case COSTCENTER_LEVEL_3:
                return COST_CENTER3;
            case COSTCENTER_LEVEL_4:
                return COST_CENTER4;
            case COSTCENTER_LEVEL_5:
                return COST_CENTER5;
            case COSTCENTER_LEVEL_6:
                return COST_CENTER6;
            default:
                return null;
        }
    }

    protected List<CostCenterInfoType> buildCostCenterInfoListNew(CostCenterInfo costCenterInfo,
        List<HotelBookPassengerInput> hotelBookPassengerInputs, CityInput cityInput,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig, Map<String, StrategyInfo> strategyInfoMap,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, HotelBookPassengerInput hotelBookPassengerInput) {
        if (costCenterInfo == null || CollectionUtil.isEmpty(costCenterInfo.getCostCenterInputs())) {
            return null;
        }
        com.ctrip.soa._20183.CostCenterInfoType costCenterInfoType =
            SaveCommonDataCostCenterInfoTypeUtil.genCostCenterInfoType(costCenterInfo.getCostCenterInputs(), "",
                OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(hotelBookPassengerInputs, cityInput,
                    checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
        return buildCostCenterInfoList(costCenterInfoType, hotelBookPassengerInput);
    }

    protected List<CostCenterInfoType> buildCostCenterInfoList(com.ctrip.soa._20183.CostCenterInfoType costCenter,
        HotelBookPassengerInput hotelBookPassengerInput) {
        if (costCenter == null || CollectionUtil.isEmpty(costCenter.getPassengerCostCenterList())) {
            return null;
        }
        PassengerCostCenterInfoType passengerCostCenterInfoType = costCenter.getPassengerCostCenterList().stream()
            .filter(passengerCostCenterInfo -> samePsg(passengerCostCenterInfo, hotelBookPassengerInput)).toList()
            .stream().findFirst().orElse(null);
        if (passengerCostCenterInfoType == null || CollectionUtil.isEmpty(
            passengerCostCenterInfoType.getCostCenterList())) {
            return null;
        }
        List<CostCenterInfoType> costCenterInfoTypes = new ArrayList<>();
        passengerCostCenterInfoType.getCostCenterList().forEach(costCenterInfoType -> {
            if (costCenterInfoType == null || StringUtil.isBlank(costCenterInfoType.getCostCenterValue())) {
                return;
            }
            String costCenterType = buildCostCenterType(costCenterInfoType.getLevel());
            if (StringUtil.isBlank(costCenterType)) {
                return;
            }
            costCenterInfoTypes.add(buildCostCenterInfoType(costCenterType, costCenterInfoType.getCostCenterValue()));
        });
        if (CollectionUtil.isEmpty(costCenterInfoTypes)) {
            return null;
        }
        return costCenterInfoTypes;
    }

    protected List<CostCenterInfoType> buildCostCenterInfoList(String costCenterStr,
        HotelBookPassengerInput passenger) {
        if (StringUtils.isBlank(costCenterStr)) {
            return null;
        }
        SaveCostCenterInputVo costCenterBean = JsonUtil.fromJsonIgnoreCase(costCenterStr, SaveCostCenterInputVo.class);
        if (costCenterBean == null || MapUtil.isEmpty(costCenterBean.getItems())) {
            return null;
        }
        if (passenger.getHotelPassengerInput() == null) {
            return null;
        }
        if (StringUtil.isBlank(passenger.getHotelPassengerInput().getUid()) && StringUtil.isBlank(
            passenger.getHotelPassengerInput().getInfoId())) {
            return null;
        }
        SaveCostCenterInputItem costCenterInputItem = costCenterBean.getItems().get(
            StringUtil.isNotBlank(passenger.getHotelPassengerInput().getUid()) ?
                passenger.getHotelPassengerInput().getUid() : passenger.getHotelPassengerInput().getInfoId());
        if (costCenterInputItem == null) {
            return null;
        }
        List<CostCenterInfoType> costCenterInfoTypes = new ArrayList<>();
        if (StringUtil.isNotBlank(costCenterInputItem.getCost1())) {
            costCenterInfoTypes.add(buildCostCenterInfoType(COST_CENTER1, costCenterInputItem.getCost1()));
        }
        if (StringUtil.isNotBlank(costCenterInputItem.getCost2())) {
            costCenterInfoTypes.add(buildCostCenterInfoType(COST_CENTER2, costCenterInputItem.getCost2()));
        }
        if (StringUtil.isNotBlank(costCenterInputItem.getCost3())) {
            costCenterInfoTypes.add(buildCostCenterInfoType(COST_CENTER3, costCenterInputItem.getCost3()));
        }
        if (StringUtil.isNotBlank(costCenterInputItem.getCost4())) {
            costCenterInfoTypes.add(buildCostCenterInfoType(COST_CENTER4, costCenterInputItem.getCost4()));
        }
        if (StringUtil.isNotBlank(costCenterInputItem.getCost5())) {
            costCenterInfoTypes.add(buildCostCenterInfoType(COST_CENTER5, costCenterInputItem.getCost5()));
        }
        if (StringUtil.isNotBlank(costCenterInputItem.getCost6())) {
            costCenterInfoTypes.add(buildCostCenterInfoType(COST_CENTER6, costCenterInputItem.getCost6()));
        }
        return costCenterInfoTypes;
    }

    private CostCenterInfoType buildCostCenterInfoType(String costCenterType, String costCenterValue) {
        CostCenterInfoType costCenterInfoType = new CostCenterInfoType();
        costCenterInfoType.setCostCenterType(costCenterType);
        costCenterInfoType.setCostCenterValue(costCenterValue);
        return costCenterInfoType;
    }

    protected static BigDecimal buildShareAmount(Map<String, BigDecimal> shareAmount, String id, String temporaryId) {
        if (MapUtils.isEmpty(shareAmount) || StringUtils.isEmpty(id)) {
            return null;
        }
        if (StringUtil.isNotBlank(temporaryId) && shareAmount.containsKey(temporaryId) && MathUtils.isGreaterThanZero(
                shareAmount.get(temporaryId))) {
            return shareAmount.get(temporaryId);
        }
        return shareAmount.get(id);
    }

    private String getSubApprovalNo(ApprovalInput approvalInput, CityInput cityInput, CorpPayInfo corpPayInfo,
                                    WrapperOfAccount.AccountInfo accountInfo) {
        String subApprovalNo =
                Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo)
                        .orElse(null);
        if (StringUtil.isBlank(subApprovalNo)) {
            return null;
        }
        if (accountInfo.isTravelApplyRequired(CityInfoUtil.oversea(cityInput.getCityId()),
                corpPayInfo)) {
            // 新版出差申请开关打开，PreApprovalNumber字段赋值为出行计划id，EndorsementNumber、TravelPlanNumber无需传入
            return subApprovalNo;
        }
        // 提前审批模式
        if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(cityInput.getCityId()),
                corpPayInfo)) {
            return subApprovalNo;
        }
        return null;
    }

    protected BookInfoType getBookInfoType(HotelInsuranceInput hotelInsuranceInput,
                                         List<HotelPayTypeInput> hotelPayTypeInputs,
                                         GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType, FlashStayInput flashStayInput,
                                         WrapperOfAccount.AccountInfo accountInfo,
                                         CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType, List<RCInput> rcInfos,
                                         String scene, ResourceToken resourceToken, ApprovalInput approvalInput, HotelPayTypeEnum selectedPayType,
        List<StrategyInfo> strategyInfos, CorpPayInfo corpPayInfo, IntegrationSoaRequestType integrationSoaRequestType) {
        BookInfoType bookInfoType = new BookInfoType();
        if (SCENE_PAY_TYPE_FROM_INPUT.equalsIgnoreCase(scene)) {
            bookInfoType.setPaymentType(buildPaymentType(selectedPayType));
            bookInfoType.setGuaranteeMethod(buildGuaranteeMethod(selectedPayType));
        } else {
            if (StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfos)) {
                bookInfoType.setPaymentType(
                    buildPaymentTypeHotelCheckAvail(hotelPayTypeInputs, accountInfo, corpPayInfo, resourceToken));
            } else {
                bookInfoType.setPaymentType(getPaymentType(
                    BookingInitUtil.getRoomPayType(selectedPayType, getSupportedPaymentMethodResponseType,
                        flashStayInput, accountInfo)));
            }
            HotelPayTypeEnum roomPayType = BookingInitUtil.getRoomPayType(selectedPayType, getSupportedPaymentMethodResponseType,
                flashStayInput, accountInfo);
            HotelGuaranteeTypeEnum hotelGuaranteeTypeEnum = BookingInitUtil.buildHotelGuaranteeTypeEnum(getSupportedPaymentMethodResponseType, hotelPayTypeInputs);
            if (!StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfos)) {
                bookInfoType.setGuaranteeMethod(buildGuaranteeMethod(roomPayType, hotelGuaranteeTypeEnum));
            }
        }
        bookInfoType.setSelectedRcTypeList(getSelectedRcList(rcInfos));
        bookInfoType.setEmergencyBooking(BooleanUtil.parseStr(true)
            .equalsIgnoreCase(Optional.ofNullable(approvalInput).map(ApprovalInput::getEmergency).orElse(null)));
        bookInfoType.setCorpXProductInfo(getCorpXProductInfos(hotelInsuranceInput, scene, strategyInfos));
        if (SCENE_PAY_TYPE_FROM_INPUT.equalsIgnoreCase(scene)) {
            HotelPayTypeEnum servicePayType = HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs, selectedPayType, resourceToken);
            bookInfoType.setServiceChargePaymentType(buildServiceChargePaymentType(hotelPayTypeInputs, selectedPayType, servicePayType));
        } else if (!StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfos)) {
            HotelPayTypeEnum servicePayType = BookingInitUtil.getServicePayType(hotelPayTypeInputs, selectedPayType, getSupportedPaymentMethodResponseType,
                flashStayInput, accountInfo, calculateServiceChargeV2ResponseType);
            bookInfoType.setServiceChargePaymentType(buildServiceChargePaymentType(hotelPayTypeInputs, selectedPayType, servicePayType));
        }
        // 降噪-国内站才需要，蓝色空间不需要，这是个啥，能抹平差异不 todo：http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
        if (integrationSoaRequestType.getUserInfo().getPos() == PosEnum.CHINA) {
            bookInfoType.setNoneAgreementRoomAvailable(buildNoneAgreementRoomAvailable(resourceToken));
        }
        return bookInfoType;
    }

    private boolean buildNoneAgreementRoomAvailable(ResourceToken resourceToken) {
        if (resourceToken == null || resourceToken.getRoomResourceToken() == null) {
            return true;
        }
        AllRoomTypeEnum allRoomType = resourceToken.getRoomResourceToken().getAllRoomType();
        return allRoomType != AllRoomTypeEnum.EXIST_C;
    }

    protected String buildServiceChargePaymentType(List<HotelPayTypeInput> hotelPayTypeInputs,
        HotelPayTypeEnum selectedPayType,
        ResourceToken resourceToken) {
        if (selectedPayType == null || selectedPayType == HotelPayTypeEnum.NONE) {
            selectedPayType = HotelPayTypeUtil.getRoomPayType(hotelPayTypeInputs);
        }
        HotelPayTypeEnum servicePayType = HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs, selectedPayType, resourceToken);
        if (!Arrays.asList(HotelPayTypeEnum.CASH, HotelPayTypeEnum.GUARANTEE_CORP_PAY,
            HotelPayTypeEnum.GUARANTEE_SELF_PAY).contains(selectedPayType)) {
            return null;
        }
        if (null == servicePayType) {
            return "UNKNOWN";
        }
        switch (servicePayType) {
            case CORP_PAY:
            case FLASH_STAY_PAY:
                return "CORP_PAY";
            case SELF_PAY:
                return "PERSONAL_PAY";
            default:
                return "UNKNOWN";
        }
    }

    protected String buildServiceChargePaymentType(List<HotelPayTypeInput> hotelPayTypeInputs,
        HotelPayTypeEnum selectedPayType,
        HotelPayTypeEnum servicePayType) {
        if (selectedPayType == null || selectedPayType == HotelPayTypeEnum.NONE) {
            selectedPayType = HotelPayTypeUtil.getRoomPayType(hotelPayTypeInputs);
        }
        if (!Arrays.asList(HotelPayTypeEnum.CASH, HotelPayTypeEnum.GUARANTEE_CORP_PAY,
            HotelPayTypeEnum.GUARANTEE_SELF_PAY).contains(selectedPayType)) {
            return null;
        }
        if (null == servicePayType) {
            return "UNKNOWN";
        }
        switch (servicePayType) {
            case CORP_PAY:
            case FLASH_STAY_PAY:
                return "CORP_PAY";
            case SELF_PAY:
                return "PERSONAL_PAY";
            default:
                return "UNKNOWN";
        }
    }

    protected List<String> getSelectedRcList(List<RCInput> rcInfos) {
        if (CollectionUtil.isEmpty(rcInfos)) {
            return null;
        }
        List<String> selectRcs = new ArrayList<>();
        rcInfos.stream().filter(Objects::nonNull).forEach(rcInfo -> {
            RcToken rcToken = TokenParseUtil.parseToken(rcInfo.getRcToken(), RcToken.class);
            if (rcToken == null || StringUtil.isEmpty(rcToken.getType())) {
                return;
            }
            RcTypeEnum rcTypeEnum = RcTypeEnum.getType(rcToken.getType());
            if (rcTypeEnum == null) {
                return;
            }
            switch (rcTypeEnum) {
                case LOW_PRICE:
                    selectRcs.add(OVER_STANDARD);
                    break;
                case AGREEMENT:
                    selectRcs.add(AGREEMENT);
                    break;
                case BOOK_AHEAD:
                    selectRcs.add(BOOK_AHEAD);
                    break;
                case CONFLICT_BOOK:
                    selectRcs.add(CONFLICT_BOOK);
                    break;
                default:
                    break;
            }
        });
        return CollectionUtil.isEmpty(selectRcs) ? null : selectRcs;
    }

    private List<CorpXProductInfoType> getCorpXProductInfos(HotelInsuranceInput hotelInsuranceInput, String scene,
        List<StrategyInfo> strategyInfos) {
        if (StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfos)) {
            return null;
        }
        if (hotelInsuranceInput == null || CollectionUtil.isEmpty(
                hotelInsuranceInput.getHotelInsuranceDetailInputs())) {
            return SCENE_PAY_TYPE_FROM_INPUT.equalsIgnoreCase(scene) ? null : new ArrayList<>();
        }
        List<CorpXProductInfoType> corpXproductInfos = new ArrayList<>();
        hotelInsuranceInput.getHotelInsuranceDetailInputs().stream().forEach(hotelInsuranceDetailInput -> {
            if (hotelInsuranceDetailInput == null || hotelInsuranceDetailInput.getInsuranceToken() == null) {
                return;
            }
            if (CollectionUtil.isEmpty(hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs())) {
                return;
            }
            CorpXProductInfoType corpXproductInfoType = new CorpXProductInfoType();
            CorpXProductInfoToken insuranceToken =
                    TokenParseUtil.parseToken(hotelInsuranceDetailInput.getInsuranceToken(), CorpXProductInfoToken.class);
            if (insuranceToken == null) {
                return;
            }
            List<CorpXProductGuestInfoType> guestList = new ArrayList<>();
            hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs().stream()
                    .forEach(insuranceHotelBookPassengerInput -> {
                        if (insuranceHotelBookPassengerInput == null
                                || insuranceHotelBookPassengerInput.getHotelPassengerInput() == null) {
                            return;
                        }
                        CorpXProductGuestInfoType corpXProductGuestInfoType = new CorpXProductGuestInfoType();
                        corpXProductGuestInfoType.setEmployee(
                                "T".equalsIgnoreCase(insuranceHotelBookPassengerInput.getHotelPassengerInput().getEmployee()));
                        corpXProductGuestInfoType.setUid(
                                insuranceHotelBookPassengerInput.getHotelPassengerInput().getUid());
                        corpXProductGuestInfoType.setRoomIndex(
                                insuranceHotelBookPassengerInput.getHotelPassengerInput().getRoomIndex());
                        guestList.add(corpXProductGuestInfoType);
                    });
            corpXproductInfoType.setGuestList(guestList);
            corpXproductInfoType.setOwnerType(PERSON);
            corpXproductInfoType.setAmountPerUnit(insuranceToken.getAmountPerUnit());
            corpXproductInfoType.setXProductId(insuranceToken.getProductId());
            corpXproductInfos.add(corpXproductInfoType);
        });
        return corpXproductInfos;
    }

    /**
     * 详情页可定对比降噪
     * @param hotelPayTypeInputs
     * @param accountInfo
     * @param corpPayInfo
     * @param resourceToken
     * @return
     */
    protected static String buildPaymentTypeHotelCheckAvail(List<HotelPayTypeInput> hotelPayTypeInputs,
        WrapperOfAccount.AccountInfo accountInfo, CorpPayInfo corpPayInfo, ResourceToken resourceToken) {
        HotelPayTypeEnum hotelPayTypeEnum = HotelPayTypeUtil.getRoomPayType(hotelPayTypeInputs);
        if (hotelPayTypeEnum == HotelPayTypeEnum.MIX_PAY) {
            return MIX_PAY;
        }
        // 蓝色空间老版：开关开了虚拟支付，传入虚拟支付，应该是个错误的，因为虚拟支付是跟资源有关系的，开关只是个基础，所以调整为通过支付方式接口判断-----虚拟支付agg只会返回getsupportedpaymentmethod虚拟支付方式
        if (hotelPayTypeEnum == HotelPayTypeEnum.PRBAL) {
            return HotelPayTypeEnum.PRBAL.getCode();
        }
        if (accountInfo.supportCorpPay(corpPayInfo, CityInfoUtil.oversea(
            Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null)), CityInfoUtil.hkOrMacao(
            Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null)), RoomTypeEnum.M.getValue().equalsIgnoreCase(
            Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken)
                .map(RoomResourceToken::getRoomType).orElse(null)))) {
            return CORP_PAY;
        }
        if (accountInfo.isOpenPersonalPayment(
            Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null), corpPayInfo, Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken)
                .map(RoomResourceToken::getRoomType).orElse(null))) {
            return PERSONAL_PAY;
        }
        return UNKNOWN;
    }

    private String buildPaymentType(HotelPayTypeEnum hotelPayTypeEnum) {
        if (hotelPayTypeEnum == null) {
            return PERSONAL_PAY;
        }
        switch (hotelPayTypeEnum) {
            case CORP_PAY:
            case ADVANCE_PAY:
            case FLASH_STAY_PAY:
                return CORP_PAY;
            case SELF_PAY:
            case CASH:
            case UNION_PAY:
            case GUARANTEE_SELF_PAY:
            case GUARANTEE_CORP_PAY:
                return PERSONAL_PAY;
            case MIX_PAY:
                return MIX_PAY;
            case CORPORATE_CARD_PAY:
            case PRBAL:
                return hotelPayTypeEnum.getCode();
            case CORP_CREDIT_CARD_GUARANTEE:
                return PERSONAL_PAY;
            default:
                return UNKNOWN;
        }
    }

    private String getPaymentType(HotelPayTypeEnum hotelPayTypeEnum) {
        if (hotelPayTypeEnum == null) {
            return UNKNOWN;
        }
        switch (hotelPayTypeEnum) {
            case CORP_PAY:
            case ADVANCE_PAY:
            case FLASH_STAY_PAY:
                return CORP_PAY;
            case SELF_PAY:
            case CASH:
            case UNION_PAY:
                return PERSONAL_PAY;
            case MIX_PAY:
                return MIX_PAY;
            case CORPORATE_CARD_PAY:
            case PRBAL:
                return hotelPayTypeEnum.getCode();
            default:
                return UNKNOWN;
        }
    }

    private AmountInfoType getAmountInfoType(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        AddPriceInput addPriceInput, HotelBookInput hotelBookInput,
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType, String scene,
        List<HotelPayTypeInput> hotelPayTypeInputs, ResourceToken resourceToken, HotelPayTypeEnum selectedRoomPayType,
        List<StrategyInfo> strategyInfos, Map<String, StrategyInfo> strategyInfoMap, HotelPayTypeEnum servicePayType,
        IntegrationSoaRequestType integrationSoaRequestType) {
        AmountInfoType amountInfoType = new AmountInfoType();
        if (StringUtil.isNotBlank(
                Optional.ofNullable(addPriceInput).map(AddPriceInput::getAmountInfo).map(AmountInfo::getAmount)
                        .orElse(null))) {
            amountInfoType.setAddPriceAmount(new BigDecimal(addPriceInput.getAmountInfo().getAmount()).multiply(
                    new BigDecimal(HotelDateRangeUtil.getRoomNights(hotelBookInput))));
        }
        amountInfoType.setCouponAmount(
            MathUtils.isGreaterThanZero(checkAvailInfo.getCouponAmount()) ? checkAvailInfo.getCouponAmount() : null);
        amountInfoType.setRoomAmount(checkAvailInfo.getRoomAmount());
        amountInfoType.setRoomAmountExcludeTax(checkAvailInfo.getRoomAmountExcludeTax());
        amountInfoType.setServiceChargeInfoList(
            getServiceChargeInfoList(calculateServiceChargeV2ResponseType, resourceToken, hotelPayTypeInputs, scene,
                strategyInfoMap));
        amountInfoType.setRoomDailyPriceList(
            buildRoomDailyPriceList(checkAvailInfo, strategyInfos, integrationSoaRequestType, scene));
        amountInfoType.setExtraPayTaxAmount(checkAvailInfo.getExtraPayTaxAmount());
        // 已经没有纸质票了 直接赋值0参与对比即可
        amountInfoType.setInvoicePostAmount(BigDecimal.ZERO);
        amountInfoType.setIntlServiceChargeInfo(
            buildIntlServiceChargeInfoType(strategyInfoMap, scene, resourceToken, calculateServiceChargeV2ResponseType,
                selectedRoomPayType, servicePayType));
        return amountInfoType;
    }

    /**
     * 暂用pos判断，对比不一致要降噪，后期技改全都接入抹平差异
     * todo：http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
     *
     * @return
     */
    protected List<RoomDailyPriceInfoType> buildRoomDailyPriceList(
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, List<StrategyInfo> strategyInfos,
        IntegrationSoaRequestType integrationSoaRequestType, String scene) {
        if (StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfos)) {
            if (integrationSoaRequestType.getUserInfo().getPos() != PosEnum.CHINA) {
                return checkAvailInfo.getRoomDailyPriceInfoTypeList();
            }
            return new ArrayList<>();
        }
        if (!SCENE_PAY_TYPE_FROM_INPUT.equalsIgnoreCase(scene)) {
            return checkAvailInfo.getRoomDailyPriceInfoTypeList();
        }
        return null;
    }

    protected IntlServiceChargeInfoType buildIntlServiceChargeInfoType(Map<String, StrategyInfo> strategyInfoMap,
        String scene, ResourceToken resourceToken,
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType, HotelPayTypeEnum roomPayType,
        HotelPayTypeEnum servicePayType) {
        if (!StrategyOfBookingInitUtil.intlServiceChargeInfo(strategyInfoMap)) {
            return null;
        }
        if (SCENE_PAY_TYPE_FROM_INPUT.equals(scene)) {
            if (servicePayType == null || servicePayType == HotelPayTypeEnum.NONE) {
                return null;
            }
            if (!MathUtils.isGreaterThanZero(Optional.ofNullable(resourceToken.getBookInitResourceToken())
                .map(BookInitResourceToken::getServiceChargeResourceToken)
                .map(ServiceChargeResourceToken::getServiceChargeAmount).orElse(null))) {
                return null;
            }
            IntlServiceChargeInfoType intlServiceChargeInfoType = new IntlServiceChargeInfoType();
            intlServiceChargeInfoType.setAmount(
                resourceToken.getBookInitResourceToken().getServiceChargeResourceToken().getServiceChargeAmount());
            if (servicePayType.isCorpPay()) {
                intlServiceChargeInfoType.setServiceChargeType(CORP_PAY_SERVICE_CHARGE);
            } else {
                intlServiceChargeInfoType.setServiceChargeType(PERSONAL_PAY_SERVICE_CHARGE);
            }
            return intlServiceChargeInfoType;
        }
        ChargeAmountInfoType chargeAmountInfoType =
            BookingInitUtil.getChargeAmountInfoType(calculateServiceChargeV2ResponseType, servicePayType, roomPayType);
        if (MathUtils.isGreaterThanZero(
            Optional.ofNullable(chargeAmountInfoType).map(ChargeAmountInfoType::getChargeAmountPack)
                .map(BaseChargeAmount::getChargeAmountCustomCurrency).map(ServiceChargePriceType::getAmount)
                .orElse(null))) {
            IntlServiceChargeInfoType intlServiceChargeInfoType = new IntlServiceChargeInfoType();
            intlServiceChargeInfoType.setAmount(
                chargeAmountInfoType.getChargeAmountPack().getChargeAmountCustomCurrency().getAmount());
            return intlServiceChargeInfoType;
        }
        return null;
    }

    private List<ServiceChargeInfoType> getServiceChargeInfoList(
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType, ResourceToken resourceToken,
        List<HotelPayTypeInput> hotelPayTypeInputs, String scene, Map<String, StrategyInfo> strategyInfoMap) {
        if (StrategyOfBookingInitUtil.intlServiceChargeInfo(strategyInfoMap)
            && StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfoMap)) {
            return null;
        }
        if (SCENE_PAY_TYPE_FROM_INPUT.equals(scene) && MathUtils.isGreaterThanZero(
            Optional.ofNullable(resourceToken).map(ResourceToken::getBookInitResourceToken)
            .map(BookInitResourceToken::getServiceChargeResourceToken)
            .map(ServiceChargeResourceToken::getServiceChargeAmount).orElse(null))) {
            ServiceChargeInfoType serviceChargeInfoType = new ServiceChargeInfoType();
            serviceChargeInfoType.setAmount(
                resourceToken.getBookInitResourceToken().getServiceChargeResourceToken().getServiceChargeAmount());
            serviceChargeInfoType.setServiceChargeType(buildServiceChargeType(hotelPayTypeInputs));
            return Arrays.asList(serviceChargeInfoType);
        }
        if (CollectionUtil.isEmpty(Optional.ofNullable(calculateServiceChargeV2ResponseType)
                .map(CalculateServiceChargeV2ResponseType::getChargeAmountInfoList).orElse(null))) {
            return null;
        }
        List<ServiceChargeInfoType> serviceChargeInfoTypeList = new ArrayList<>();
        calculateServiceChargeV2ResponseType.getChargeAmountInfoList().stream().filter(Objects::nonNull)
                .forEach(serviceChargeInfo -> {
                    if (serviceChargeInfo.getChargeAmountPack() == null
                            || serviceChargeInfo.getChargeAmountPack().getChargeAmountCustomCurrency() == null
                            || serviceChargeInfo.getChargeAmountPack().getChargeAmountCustomCurrency().getAmount() == null
                            || !StringUtil.equalsIgnoreCase(CHARGE_MOMENT_PRE, serviceChargeInfo.getChargeMoment())) {
                        return;
                    }
                    ServiceChargeInfoType serviceChargeInfoType = new ServiceChargeInfoType();
                    serviceChargeInfoType.setAmount(
                            serviceChargeInfo.getChargeAmountPack().getChargeAmountCustomCurrency().getAmount());
                    serviceChargeInfoType.setServiceChargeType(
                            StringUtil.equalsIgnoreCase(ACCOUNT_PAY, serviceChargeInfo.getPaymentMethod()) ?
                                    CORP_PAY_SERVICE_CHARGE : PERSONAL_PAY_SERVICE_CHARGE);
                    serviceChargeInfoTypeList.add(serviceChargeInfoType);
                });
        return CollectionUtil.isEmpty(serviceChargeInfoTypeList) ? null : serviceChargeInfoTypeList;
    }

    /**
     * 前端入参客户有选中服务支付方式 以客户选中为主
     * 客户未选中 以房费支付方式为准---todo：这里可能有点小问题 例如混付的时候客户也不选但应该是公帐 为了降噪先同老逻辑
     * 调研清楚后最好换用 HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs, selectedRoomPayType, resourceToken) 统一处理
     *
     * @return
     */
    private String buildServiceChargeType(List<HotelPayTypeInput> hotelPayTypeInputs) {
        HotelPayTypeEnum serviceFeeType = HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs);
        if (serviceFeeType == HotelPayTypeEnum.CORP_PAY) {
            return CORP_PAY_SERVICE_CHARGE;
        }
        if (serviceFeeType == HotelPayTypeEnum.SELF_PAY) {
            return PERSONAL_PAY_SERVICE_CHARGE;
        }
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(hotelPayTypeInputs);
        if (roomPayType == HotelPayTypeEnum.CORP_PAY) {
            return CORP_PAY_SERVICE_CHARGE;
        }
        return PERSONAL_PAY_SERVICE_CHARGE;
    }

    private BaseInfoType getBaseInfoType(IntegrationSoaRequestType integrationSoaRequestType,
                                         HotelPolicyInput policyInput, String scene,
        WrapperOfAccount.AccountInfo accountInfo) {
        BaseInfoType baseInfoType = new BaseInfoType();
        baseInfoType.setLanguage(integrationSoaRequestType.getLanguage());
        baseInfoType.setPolicyUid(buildPolicyUid(policyInput, scene, accountInfo));
        // baseInfoType.setLocale(integrationSoaRequestType.getLanguage());
        baseInfoType.setUid(integrationSoaRequestType.getUserInfo().getUserId());
        baseInfoType.setCorpId(integrationSoaRequestType.getUserInfo().getCorpId());
        baseInfoType.setBookingChannel(getBookingChannel(integrationSoaRequestType));
        if (SCENE_PAY_TYPE_FROM_INPUT.equals(scene)) {
            baseInfoType.setScenario(RC_APPROVAL_CONTROL);
        } else {
            baseInfoType.setScenario(RC_CONTROL);
        }
        return baseInfoType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<WrapperOfCheckTravelPolicy> typeTuple) {
        return null;
    }

    private String buildPolicyUid(HotelPolicyInput policyInput, String scene,
        WrapperOfAccount.AccountInfo accountInfo) {
        if (SCENE_PAY_TYPE_FROM_INPUT.equals(scene)) {
            if (accountInfo.isPolicyModel()) {
                return Optional.ofNullable(policyInput).map(HotelPolicyInput::getPolicyInput)
                    .map(PolicyInput::getPolicyUid).orElse(StringUtil.EMPTY);
            }
            return null;
        }
        return Optional.ofNullable(policyInput).map(HotelPolicyInput::getPolicyInput).map(PolicyInput::getPolicyUid)
            .orElse(StringUtil.EMPTY);
    }

    private String getBookingChannel(IntegrationSoaRequestType integrationSoaRequestType) {
        switch (integrationSoaRequestType.getSourceFrom()) {
            case Online:
                return ONLINE;
            case Offline:
                return OFFLINE;
            default:
                return APP;
        }
    }

    private String buildGuaranteeMethod(HotelPayTypeEnum payType) {
        if (HotelPayTypeEnum.CASH == payType) {
            return NONE;
        }
        if (!Arrays.asList(HotelPayTypeEnum.GUARANTEE_CORP_PAY, HotelPayTypeEnum.GUARANTEE_SELF_PAY)
            .contains(payType)) {
            return null;
        }
        switch (payType) {
            case GUARANTEE_CORP_PAY:
                return ACCOUNT_GUARANTEE;
            case GUARANTEE_SELF_PAY:
                return INDIVIDUAL_GUARANTEE;
            default:
                return NONE;
        }
    }

    private String buildGuaranteeMethod(HotelPayTypeEnum roomPayType, HotelGuaranteeTypeEnum guaranteePayType) {
        if (HotelPayTypeEnum.CASH != roomPayType) {
            return null;
        }
        if (guaranteePayType == null) {
            return NONE;
        }
        switch (guaranteePayType) {
            case CORP_GUARANTEE:
                return ACCOUNT_GUARANTEE;
            case SELF_GUARANTEE:
                return INDIVIDUAL_GUARANTEE;
            default:
                return NONE;
        }
    }
}
