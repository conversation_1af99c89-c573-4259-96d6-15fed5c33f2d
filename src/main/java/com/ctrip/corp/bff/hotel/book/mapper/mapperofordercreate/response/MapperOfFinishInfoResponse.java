package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PayAmountResult;
import com.ctrip.corp.bff.framework.template.common.language.LanguageUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.CompletionPageUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfFinishResponse;
import com.ctrip.corp.bff.hotel.book.contract.BookUrlInfo;
import com.ctrip.corp.bff.hotel.book.contract.FinishInfoOutput;
import com.ctrip.corp.bff.hotel.book.contract.HotelTripOutput;
import com.ctrip.corp.bff.hotel.book.contract.NativePayInfo;
import com.ctrip.corp.bff.hotel.book.contract.OfflinePayInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.PayMentInfo;
import com.ctrip.corp.bff.hotel.book.contract.PayMentInfoInput;
import com.ctrip.corp.bff.hotel.book.contract.UnionPayInfo;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateResponseType;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubmitOrderResponseType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeResponseType;
import com.ctrip.microfinance.giftcardpay.ws.contract.dtotypes.TicketItemTypeVo;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa._21685.TransactionPayUrlResponseType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/28 22:27
 */
@Component public class MapperOfFinishInfoResponse
    extends AbstractMapper<Tuple1<WrapperOfFinishResponse>, Tuple2<Boolean, OrderCreateResponseType>> {
    // 银联sdk支付
    public static final String UNION_PAY = "UNION_PAY";
    // 客户收银台支付丰享
    public static final String DOUBLE_PAY = "DOUBLE_PAY";
    // 散客表单收银台支付
    public static final String FROM_PAY = "FROM_PAY";
    // 普通H5/pc散客收银台支付
    public static final String NORMAL_PAY = "NORMAL_PAY";
    // 壳内native支付
    public static final String NATIVE_PAY = "NATIVE_PAY";
    // 微信小程序sdk支付
    public static final String MINI_APP = "MINI_APP";
    // 闪住sdk授权
    public static final String FLASH_PAY = "FLASH_PAY";
    // 闪住跳转授权
    public static final String FLASH_PAY_H5 = "FLASH_PAY_H5";


    private static final String FINISH_TYPE_PAY = "PAY";

    private static final String FINISH = "FINISH";

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple1<WrapperOfFinishResponse> request) {
        WrapperOfFinishResponse wrapperOfFinishResponse = request.getT1();
        OrderCreateRequestType orderCreateRequestType = wrapperOfFinishResponse.getOrderCreateRequestType();
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType =
            wrapperOfFinishResponse.getTmsCreateOrderVerifyResponseType();
        CreateOrderResponseType createOrderResponseType = wrapperOfFinishResponse.getCreateOrderResponseType();
        PaymentOrderCreateResponseType paymentOrderCreateResponseType =
            wrapperOfFinishResponse.getPaymentOrderCreateResponseType();
        ResourceToken resourceToken = wrapperOfFinishResponse.getResourceToken();
        OrderCreateToken orderCreateToken = wrapperOfFinishResponse.getOrderCreateToken();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfFinishResponse.getAccountInfo();
        CreateTripResponseType createTripResponseType = wrapperOfFinishResponse.getCreateTripResponseType();
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType =
            Optional.ofNullable(wrapperOfFinishResponse.getQueryPaymentBillConfigResponseType())
                .orElse(new QueryPaymentBillConfigResponseType());
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo = wrapperOfFinishResponse.getCheckAvailContextInfo();
        RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType =
            wrapperOfFinishResponse.getRetrieveTicketsByOrderTypeResponseType();
        TransactionPayUrlResponseType transactionPayUrlResponseType =
            wrapperOfFinishResponse.getTransactionPayUrlResponseType();
        PayConfigResponseType payConfigResponseType = wrapperOfFinishResponse.getPayConfigResponseType();
        if (!orderCreateToken.isUseOrderCreate()) {
            paymentOrderCreateResponseType = new PaymentOrderCreateResponseType();
        }
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        if (HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput())
            == HotelPayTypeEnum.FLASH_STAY_PAY) {
            // 闪住授权---闪住唤起后付收银台 依赖订单createOrder接口
            orderCreateResponseType.setFinishInfo(
                buildFinishInfoOutputFlashPay(createOrderResponseType, orderCreateRequestType, orderCreateToken,
                    queryPaymentBillConfigResponseType));
            return Tuple2.of(true, orderCreateResponseType);
        }
        if (OrderCreateProcessorOfUtil.requireDoublePay(orderCreateRequestType, payConfigResponseType)) {
            // 丰享双付---依赖订单双付接口
            orderCreateResponseType.setFinishInfo(
                buildFinishInfoOutputDoublePay(createOrderResponseType, orderCreateToken, orderCreateRequestType,
                    transactionPayUrlResponseType));
            return Tuple2.of(true, orderCreateResponseType);
        }
        if (OrderCreateProcessorOfUtil.requirePaymentOrderCreate(orderCreateRequestType, resourceToken,
            orderCreateToken)) {
            // 支付操作---依赖支付BFF基础支付
            orderCreateResponseType.setFinishInfo(
                buildFinishInfoOutputSelfPay(orderCreateRequestType, createOrderResponseType,
                    paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType));
            return Tuple2.of(true, orderCreateResponseType);
        }
        if (OrderCreateProcessorOfUtil.requireFromPay(orderCreateRequestType, resourceToken, orderCreateToken)) {
            // 表单支付---自己拼接表单
            orderCreateResponseType.setFinishInfo(
                buildFinishInfoOutputFromPay(orderCreateRequestType, createOrderResponseType, orderCreateToken,
                    queryPaymentBillConfigResponseType, resourceToken, checkAvailInfo,
                    retrieveTicketsByOrderTypeResponseType, accountInfo));
            return Tuple2.of(true, orderCreateResponseType);
        }
        // 其他场景---流程结束
        orderCreateResponseType.setFinishInfo(
            buildFinishInfoOutput(accountInfo, orderCreateRequestType, createTripResponseType, createOrderResponseType,
                tmsCreateOrderVerifyResponseType, orderCreateToken));
        return Tuple2.of(true, orderCreateResponseType);
    }

    @Override protected ParamCheckResult check(Tuple1<WrapperOfFinishResponse> request) {
        WrapperOfFinishResponse wrapperOfFinishResponse = request.getT1();
        OrderCreateRequestType orderCreateRequestType = wrapperOfFinishResponse.getOrderCreateRequestType();
        ResourceToken resourceToken = wrapperOfFinishResponse.getResourceToken();
        PaymentOrderCreateResponseType paymentOrderCreateResponseType =
            wrapperOfFinishResponse.getPaymentOrderCreateResponseType();
        PayConfigResponseType payConfigResponseType = wrapperOfFinishResponse.getPayConfigResponseType();
        OrderCreateToken orderCreateToken = wrapperOfFinishResponse.getOrderCreateToken();
        TransactionPayUrlResponseType transactionPayUrlResponseType =
            wrapperOfFinishResponse.getTransactionPayUrlResponseType();
        if (!orderCreateToken.isUseOrderCreate()) {
            return null;
        }
        if (OrderCreateProcessorOfUtil.requirePaymentOrderCreate(orderCreateRequestType,
            wrapperOfFinishResponse.getResourceToken(), orderCreateToken)) {
            if (!checkPaymentOrderCreate(paymentOrderCreateResponseType)) {
                return OrderCreateProcessorOfUtil.buildParamCheckResult(
                    Optional.ofNullable(paymentOrderCreateResponseType)
                        .map(PaymentOrderCreateResponseType::getIntegrationResponse).orElse(null),
                    OrderCreateErrorEnum.PAYMENT_ORDERCREATE_ERROR,
                    SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_BFF_PAYMENT_SERVICE,
                    SoaErrorSharkKeyConstant.ACTION_NAME_PAYMENT_ORDER_CREATE);
            }
        }
        if (OrderCreateProcessorOfUtil.requireDoublePay(orderCreateRequestType, payConfigResponseType)) {
            // Integer errorCode qconfig map的结果, String logErrorCode 接口的errorCode
            if (!Optional.ofNullable(transactionPayUrlResponseType).map(TransactionPayUrlResponseType::getResponseCode)
                .orElse(0).equals(CommonConstant.SUCCESS_20000)) {
                Integer logErrorCode = Optional.ofNullable(transactionPayUrlResponseType)
                    .map(TransactionPayUrlResponseType::getResponseCode)
                    .orElse(OrderCreateErrorEnum.DOUBLE_PAY_NON_URL.getErrorCode());
                String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                    SoaErrorSharkKeyConstant.SERVICE_NAME_ORDER_PAYMENT_CENTER_TRANSACTION_SERVICE,
                    SoaErrorSharkKeyConstant.ACTION_NAME_TRANSACTION_PAY_URL, String.valueOf(logErrorCode));
                // 目前客户端无有特殊定制时 使用qConfigOfCodeMappingConfig映射
                Integer errorCode = OrderCreateErrorEnum.DOUBLE_PAY_NON_URL.getErrorCode();
                return new ParamCheckResult(false, errorCode, String.valueOf(logErrorCode),
                    Optional.ofNullable(transactionPayUrlResponseType)
                        .map(TransactionPayUrlResponseType::getResponseDesc).orElse(null), friendlyMessage);
            }
        }
        return null;
    }


    protected FinishInfoOutput buildFinishInfoOutputPayBase(CreateOrderResponseType createOrderResponseType,
        OrderCreateToken orderCreateToken) {
        FinishInfoOutput finishInfoOutput = new FinishInfoOutput();
        // 完成类型 流程结束：FINISH 支付操作：PAY 闪住授权：FLASH_PAY
        finishInfoOutput.setFinishType(FINISH_TYPE_PAY);
        finishInfoOutput.setOrderId(
            String.valueOf(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        return finishInfoOutput;
    }

    protected FinishInfoOutput buildFinishInfoOutputDoublePay(CreateOrderResponseType createOrderResponseType,
        OrderCreateToken orderCreateToken, OrderCreateRequestType orderCreateRequestType,
        TransactionPayUrlResponseType transactionPayUrlResponseType) {
        FinishInfoOutput finishInfoOutput = buildFinishInfoOutputPayBase(createOrderResponseType, orderCreateToken);
        PayMentInfo payMentInfo = new PayMentInfo();
        payMentInfo.setPayType(DOUBLE_PAY);
        payMentInfo.setRedirectUrl(
            Optional.ofNullable(transactionPayUrlResponseType).map(TransactionPayUrlResponseType::getPayUrl)
                .orElse(null));
        finishInfoOutput.setPayMentInfo(payMentInfo);
        finishInfoOutput.setCompletionPageUrl(CompletionPageUtil.buildCompletionPageUrl(
            orderCreateRequestType,
            OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        return finishInfoOutput;
    }

    protected FinishInfoOutput buildFinishInfoOutputFlashPay(CreateOrderResponseType createOrderResponseType,
        OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        FinishInfoOutput finishInfoOutput = new FinishInfoOutput();
        finishInfoOutput.setFinishType(FLASH_PAY);
        finishInfoOutput.setOrderId(
            String.valueOf(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        PayMentInfo payMentInfo = new PayMentInfo();
        payMentInfo.setMerchantId(Optional.ofNullable(queryPaymentBillConfigResponseType)
            .map(QueryPaymentBillConfigResponseType::getMerchantId).orElse(null));
        payMentInfo.setPayType(buildPayTypeFlashPay(orderCreateRequestType));
        payMentInfo.setFlashStayInput(orderCreateRequestType.getFlashStayInput());
        if (buildPayTypeFlashPayH5(orderCreateRequestType)) {
            payMentInfo.setRedirectUrl(createOrderResult.getPayLink());
        } else {
            payMentInfo.setPayToken(createOrderResult.getPayToken());
        }
        finishInfoOutput.setPayMentInfo(payMentInfo);
        finishInfoOutput.setCompletionPageUrl(CompletionPageUtil.buildCompletionPageUrl(
            orderCreateRequestType,
            OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        return finishInfoOutput;
    }

    protected boolean buildPayTypeFlashPayH5(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getFlashStayInput() == null) {
            return false;
        }
        if ("H5".equalsIgnoreCase(orderCreateRequestType.getFlashStayInput().getFlashPlatFrom())) {
            return true;
        }
        if ("APP".equalsIgnoreCase(orderCreateRequestType.getFlashStayInput().getFlashPlatFrom())) {
            return false;
        }
        return false;
    }

    protected String buildPayTypeFlashPay(OrderCreateRequestType orderCreateRequestType) {
        if (buildPayTypeFlashPayH5(orderCreateRequestType)) {
            return FLASH_PAY_H5;
        }
        return FLASH_PAY;
    }

    protected FinishInfoOutput buildFinishInfoOutputFromPay(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType, ResourceToken resourceToken,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo,
        RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType,
        WrapperOfAccount.AccountInfo accountInfo) {
        FinishInfoOutput finishInfoOutput = buildFinishInfoOutputPayBase(createOrderResponseType, orderCreateToken);
        PayMentInfo payMentInfo = new PayMentInfo();
        payMentInfo.setPayType(FROM_PAY);
        OfflinePayInfo offlinePayInfo = new OfflinePayInfo();
        String offlineRequest = getRequestStr(orderCreateRequestType, createOrderResponseType, orderCreateToken,
            queryPaymentBillConfigResponseType, resourceToken, checkAvailInfo, retrieveTicketsByOrderTypeResponseType,
            accountInfo);
        offlinePayInfo.setOfflineRequest(offlineRequest);
        payMentInfo.setOfflinePayInfo(offlinePayInfo);
        // 本身表单支付就是个临时方案而已 以后会接新金融平台的 所以公共的表单重定向地址还是放在vo处理了 payMentInfo.setRedirectUrl();
        finishInfoOutput.setPayMentInfo(payMentInfo);
        finishInfoOutput.setCompletionPageUrl(CompletionPageUtil.buildCompletionPageUrl(
            orderCreateRequestType,
            OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        return finishInfoOutput;
    }

    protected FinishInfoOutput buildFinishInfoOutputSelfPay(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, PaymentOrderCreateResponseType paymentOrderCreateResponseType,
        OrderCreateToken orderCreateToken, QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType) {
        FinishInfoOutput finishInfoOutput = buildFinishInfoOutputPayBase(createOrderResponseType, orderCreateToken);
        PayMentInfo payMentInfo = new PayMentInfo();
        payMentInfo.setMerchantId(Optional.ofNullable(queryPaymentBillConfigResponseType)
            .map(QueryPaymentBillConfigResponseType::getMerchantId).orElse(null));
        payMentInfo.setBusType(Optional.ofNullable(queryPaymentBillConfigResponseType)
            .map(QueryPaymentBillConfigResponseType::getBusType).orElse(null));
        payMentInfo.setNativePayInfo(buildNativePayInfo(orderCreateRequestType, createOrderResponseType,
            paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType));
        payMentInfo.setUnionPayInfo(buildUnionPayInfo(orderCreateRequestType, paymentOrderCreateResponseType));
        payMentInfo.setPayType(
            buildPayType(orderCreateRequestType, createOrderResponseType, paymentOrderCreateResponseType,
                orderCreateToken, queryPaymentBillConfigResponseType));
        payMentInfo.setRedirectUrl(paymentOrderCreateResponseType.getPayLinkUrl());
        payMentInfo.setPayToken(paymentOrderCreateResponseType.getPayToken());
        finishInfoOutput.setPayMentInfo(payMentInfo);
        finishInfoOutput.setCompletionPageUrl(CompletionPageUtil.buildCompletionPageUrl(
            orderCreateRequestType,
            OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        return finishInfoOutput;
    }

    protected UnionPayInfo buildUnionPayInfo(OrderCreateRequestType orderCreateRequestType,
        PaymentOrderCreateResponseType paymentOrderCreateResponseType) {
        if (!buildUnionPay(orderCreateRequestType)) {
            return null;
        }
        UnionPayInfo nativePayInfo = new UnionPayInfo();
        nativePayInfo.setExternalTransNo(paymentOrderCreateResponseType.getUnionTransNo());
        nativePayInfo.setSceneCode(paymentOrderCreateResponseType.getUnionSceneCode());
        return nativePayInfo;
    }

    protected boolean buildUnionPay(OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        return roomPayType == HotelPayTypeEnum.UNION_PAY;
    }

    protected NativePayInfo buildNativePayInfo(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, PaymentOrderCreateResponseType paymentOrderCreateResponseType,
        OrderCreateToken orderCreateToken, QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType) {
        if (!isNativePay(orderCreateRequestType, createOrderResponseType, orderCreateToken) && StringUtil.isNotBlank(
            Optional.ofNullable(paymentOrderCreateResponseType).map(PaymentOrderCreateResponseType::getPayToken)
                .orElse(null))) {
            return null;
        }
        NativePayInfo nativePayInfo = new NativePayInfo();
        nativePayInfo.setNativePay(buildNativePay(paymentOrderCreateResponseType, createOrderResponseType,
            orderCreateToken, orderCreateRequestType, queryPaymentBillConfigResponseType));
        return nativePayInfo;
    }


    private static final String ORDER_PAYMENT_CLIENT_TYPE_MINIAPP = "MiniApp";
    /**
     * 走商旅BFF公共的 支付类型
     * UNION_PAY:银联sdk支付
     * NORMAL_PAY:普通H5/pc散客收银台支付
     * NATIVE_PAY:壳内native支付
     * MINI_APP:微信小程序sdk支付
     * CLIENT_MINI_APP:客户小程序对接sdk支付
     * @param orderCreateRequestType
     * @param createOrderResponseType
     * @param paymentOrderCreateResponseType
     * @param orderCreateToken
     * @param queryPaymentBillConfigResponseType
     * @return
     */
    protected String buildPayType(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, PaymentOrderCreateResponseType paymentOrderCreateResponseType,
        OrderCreateToken orderCreateToken, QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType) {
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Online
            || orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Offline) {
            return NORMAL_PAY;
        }
        if (StringUtil.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput()).map(PayMentInfoInput::getClientType)
                .orElse(null), ORDER_PAYMENT_CLIENT_TYPE_MINIAPP)) {
            return MINI_APP;
        }
        if (isNativePay(orderCreateRequestType, createOrderResponseType, orderCreateToken) && StringUtil.isNotBlank(
            paymentOrderCreateResponseType.getPayToken())) {
            return NATIVE_PAY;
        }
        if (buildUnionPay(orderCreateRequestType)) {
            return UNION_PAY;
        }
        return NORMAL_PAY;
    }

    protected String buildNativePay(PaymentOrderCreateResponseType paymentOrderCreateResponseType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        PaymentNativeInfo paymentNativeInfo = new PaymentNativeInfo();
        paymentNativeInfo.setMerchantId(queryPaymentBillConfigResponseType.getMerchantId());
        paymentNativeInfo.setOrderId(String.valueOf(createOrderResult.getOrderID()));
        paymentNativeInfo.setPayToken(
            Optional.ofNullable(paymentOrderCreateResponseType).map(PaymentOrderCreateResponseType::getPayToken)
                .orElse(null));
        paymentNativeInfo.setRequestId(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        return JsonUtil.toJson(paymentNativeInfo);
    }

    protected boolean isNativePay(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken) {
        if (StringUtil.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput()).map(PayMentInfoInput::getClientType)
                .orElse(null), "H5")) {
            return false;
        }
        if (!orderCreateRequestType.getIntegrationSoaRequestType().getLanguage()
            .equalsIgnoreCase(LanguageLocaleEnum.ZH_CN.getLanguageLocaleString())) {
            return false;
        }
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        String payCurrency =
            Optional.ofNullable(createOrderResult.getPayAmountResult()).map(PayAmountResult::getCurrency).orElse(null);
        if (StringUtil.equalsIgnoreCase(payCurrency, "CNY")) {
            return true;
        }
        if (StringUtil.equalsIgnoreCase(payCurrency, "RMB")) {
            return true;
        }
        return false;
    }

    private boolean checkPaymentOrderCreate(PaymentOrderCreateResponseType paymentOrderCreateResponseType) {
        if (paymentOrderCreateResponseType == null) {
            return false;
        }
        if (StringUtil.isBlank(paymentOrderCreateResponseType.getPayLinkUrl()) && StringUtil.isBlank(
            paymentOrderCreateResponseType.getPayToken())) {
            return false;
        }
        return true;
    }

    protected FinishInfoOutput buildFinishInfoOutput(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType, CreateTripResponseType createTripResponseType,
        CreateOrderResponseType createOrderResponseType,
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType, OrderCreateToken orderCreateToken) {
        FinishInfoOutput finishInfoOutput = new FinishInfoOutput();
        String activityDetailUrl = Optional.ofNullable(tmsCreateOrderVerifyResponseType)
            .map(TmsCreateOrderVerifyResponseType::getActivityDetailUrl).orElse(null);
        if (StringUtil.isNotBlank(activityDetailUrl)) {
            finishInfoOutput.setSuccessUrl(activityDetailUrl);
        }
        finishInfoOutput.setFinishType(FINISH);
        finishInfoOutput.setOrderId(
            String.valueOf(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        finishInfoOutput.setHotelTripOutput(buildHotelTripOutput(accountInfo, orderCreateRequestType,
            createTripResponseType, orderCreateToken));
        finishInfoOutput.setCompletionPageUrl(StringUtil.isNotBlank(activityDetailUrl) ?
            activityDetailUrl : CompletionPageUtil.buildCompletionPageUrl(
                orderCreateRequestType,
            OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        return finishInfoOutput;
    }

    protected HotelTripOutput buildHotelTripOutput(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType, CreateTripResponseType createTripResponseType,
        OrderCreateToken orderCreateToken) {
        Long tripId = OrderCreateProcessorOfUtil.getTripId(accountInfo, orderCreateRequestType, createTripResponseType,
            orderCreateToken);
        if (TemplateNumberUtil.getValue(tripId) <= 0) {
            return null;
        }
        HotelTripOutput hotelTripOutput = new HotelTripOutput();
        hotelTripOutput.setTripId(String.valueOf(tripId));
        hotelTripOutput.setFollow(BooleanUtil.parseStr(
            OrderCreateProcessorOfUtil.buildIsTripFollow(orderCreateRequestType, orderCreateToken)));
        return hotelTripOutput;
    }

    private static final String VERSION = "2.01";
    // iframe传22，非iframe传21
    private static final String PLATFORM = "21";
    private static final String INTERFACE_NAME = "Create_PaymentBill";

    private static final String SUPPORT = "1";
    private static final String UN_SUPPORT = "0";
    // 目前offline和系统外只接了任我行，任我游两种
    private static final String TICKET_TYPE = "2,3";

    public static final int PAYEE_TO_CTRIP = 1;
    public static final int PAYEE_TO_HOTEL = 2;

    protected String getRequestStr(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType, ResourceToken resourceToken,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo,
        RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType,
        WrapperOfAccount.AccountInfo accountInfo) {
        return JsonUtil.toJson(getPayRequestInfo(orderCreateRequestType, createOrderResponseType, orderCreateToken,
            queryPaymentBillConfigResponseType, resourceToken, checkAvailInfo, retrieveTicketsByOrderTypeResponseType,
            accountInfo));
    }

    /**
     * 完成页灰度开关
     */
    private static final String USE_NEW_COMPLETION_PAGE = "useNewCompletionPage";

    protected PayRequestInfo getPayRequestInfo(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType, ResourceToken resourceToken,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo,
        RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType,
        WrapperOfAccount.AccountInfo accountInfo) {
        PayRequestInfo payRequestInfo = new PayRequestInfo();
        payRequestInfo.setVersion(VERSION);
        payRequestInfo.setPlatform(PLATFORM);
        payRequestInfo.setInterfaceName(INTERFACE_NAME);
        // payRequestInfo.setTransactionID(buildPaymentId(createOrderResponseType, orderCreateToken));
        payRequestInfo.setTransactionID(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        payRequestInfo.setExternalNo(null);

        String bookUrl = buildBookUrl(orderCreateRequestType.getBookUrlInfos(),"OFFLINE_RETURN_URL");
        if (QConfigOfCustomConfig.isSupport(USE_NEW_COMPLETION_PAGE,
            RequestHeaderUtil.getCorpId(orderCreateRequestType.getIntegrationSoaRequestType()))) {
            bookUrl = CompletionPageUtil.getNewCompletionPageUrl(
                orderCreateRequestType,
                OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType));
        }
        payRequestInfo.setReturnUrl(bookUrl);
        payRequestInfo.setLanguage(
            convertToPaymentLanguage(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage()));
        payRequestInfo.setOrderTitleInfo(null);
        payRequestInfo.setOrderPromptInfo(null);
        payRequestInfo.setPaymentDescription(
            Optional.ofNullable(resourceToken.getBookInitResourceToken()).map(BookInitResourceToken::getCancelTip)
                .orElse(null));
        payRequestInfo.setPreviousStepUrl(null);
        payRequestInfo.setInvoiceInfoUrl(null);
        payRequestInfo.setOrderID(
            String.valueOf(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (checkAvailInfo.getHotelBalanceTypeEnum().isFG() && !OrderCreateProcessorOfUtil.buildOnlyServiceSelfPay(
            orderCreateRequestType,
            resourceToken)) {
            payRequestInfo.setGuaranteeAmount(
                Optional.ofNullable(createOrderResult.getPayAmountResult().getAmount()).orElse(BigDecimal.ZERO)
                    .toString());
            payRequestInfo.setGuaranteeCurrency(createOrderResult.getPayAmountResult().getCurrency());
            payRequestInfo.setIsGuaranteePay("1");
        }
        payRequestInfo.setAmount(
            Optional.ofNullable(createOrderResult.getPayAmountResult().getAmount()).orElse(BigDecimal.ZERO).toString());
        payRequestInfo.setCurrencyCode(createOrderResult.getPayAmountResult().getCurrency());
        payRequestInfo.setIsNeedPreAuth(
            checkAvailInfo.getPaymentGuaranteePolyEnum().isPayToCtrip() ? SUPPORT : UN_SUPPORT);
        payRequestInfo.setMarketingPlan(null);
        payRequestInfo.setCustomerID(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        payRequestInfo.setCustomerName(null);
        // 允许现金余额支付，0：不允许；1：允许；默认值是1
        payRequestInfo.setEnableCashAccountPay("0");
        if (buildTravelMoneyPay(accountInfo, checkAvailInfo, orderCreateRequestType,
            retrieveTicketsByOrderTypeResponseType)) {
            // 允许礼品卡支付，0：不允许；1：允许；默认值是0
            payRequestInfo.setEnableTicketPay(SUPPORT);
            // 礼品卡类型，多个以逗号隔开，2：任我游，3：任我行
            payRequestInfo.setTicketType(TICKET_TYPE);
        }
        payRequestInfo.setEnabledCardExpirationDate(null);
        payRequestInfo.setCreditCardNoRange(null);
        payRequestInfo.setCardHolder(null);
        payRequestInfo.setEnableInvoice(null);
        payRequestInfo.setRelatedTransactionID(null);
        payRequestInfo.setPayRequestExtend(
            getPayExtendInfo(queryPaymentBillConfigResponseType, checkAvailInfo, orderCreateRequestType,
                resourceToken));
        if (CollectionUtil.isNotEmpty(queryPaymentBillConfigResponseType.getDisabledPayWayList())) {
            payRequestInfo.setDisabledPayWay(
                StringUtils.join(queryPaymentBillConfigResponseType.getDisabledPayWayList(), ","));
            payRequestInfo.setEnabledPayCatalog("");
        }
        if (CollectionUtil.isNotEmpty(queryPaymentBillConfigResponseType.getEnabledPayWayList())) {
            payRequestInfo.setEnabledPayWay(
                StringUtils.join(queryPaymentBillConfigResponseType.getEnabledPayWayList(), ","));
            payRequestInfo.setEnabledPayCatalog("");
        }
        payRequestInfo.setMerchantID(queryPaymentBillConfigResponseType.getMerchantId());
        payRequestInfo.setMerchantKey(queryPaymentBillConfigResponseType.getMerchantKey());
        payRequestInfo.getPayRequestExtend().setPaymentNotify(queryPaymentBillConfigResponseType.getNotifyUrl());
        payRequestInfo.getPayRequestExtend().setRecallType(queryPaymentBillConfigResponseType.getRecallUrl());
        return payRequestInfo;
    }

    /**
     * 设置支付请求扩展信息
     */
    private PayExtendInfo getPayExtendInfo(QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo, OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken) {
        PayExtendInfo payExtendInfo = new PayExtendInfo();
        boolean payToCtrip = isPayToCtrip(checkAvailInfo, orderCreateRequestType, resourceToken);
        // 协议酒店信用卡担保走实时支付
        payExtendInfo.setIsRealTimePay(1);
        payExtendInfo.setIsAutoApplyBill(1);
        // 是否使用app版paymentNotify契约，1使用，其他不使用
        payExtendInfo.setIsAppPaymentNotify("1");
        // 1：付款到携程 2：付款到酒店
        payExtendInfo.setPayee(payToCtrip ? PAYEE_TO_CTRIP : PAYEE_TO_HOTEL);
        // 设置回调地址
        payExtendInfo.setPaymentNotify(queryPaymentBillConfigResponseType.getNotifyUrl());
        payExtendInfo.setRecallType(queryPaymentBillConfigResponseType.getRecallUrl());
        payExtendInfo.setReturnMethod("GET");
        return payExtendInfo;
    }

    protected boolean isPayToCtrip(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        if (checkAvailInfo.getHotelBalanceTypeEnum().isPP() || checkAvailInfo.getHotelBalanceTypeEnum().isUseFG()) {
            return true;
        }
        if (OrderCreateProcessorOfUtil.buildOnlyServiceSelfPay(orderCreateRequestType, resourceToken)) {
            return true;
        }
        return !checkAvailInfo.getPaymentGuaranteePolyEnum().isPayToHotel();
    }

    // 是否允许礼品卡支付
    protected boolean buildTravelMoneyPay(WrapperOfAccount.AccountInfo accountInfo,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, OrderCreateRequestType orderCreateRequestType,
        RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType) {
        // 因公不支持---新版产品要求
        if (CorpPayInfoUtil.isPublic(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (checkAvailInfo.getRoomTypeEnum() == RoomTypeEnum.C) {
            return false;
        }
        if (checkAvailInfo.isTmcPrice()) {
            return false;
        }
        // 不是预付
        if (!(checkAvailInfo.getHotelBalanceTypeEnum().isPP() || checkAvailInfo.getHotelBalanceTypeEnum().isUseFG())) {
            return false;
        }
        // 可订检查接口返回不支持
        if (!BooleanUtils.isTrue(checkAvailInfo.isCanTravelMoneyPay())) {
            return false;
        }
        // 多币种不支持
        if (OrderCreateProcessorOfUtil.buildMultiCurrency(accountInfo.settlementCurrencyForAgg())) {
            return false;
        }
        return hasTicketAmount(retrieveTicketsByOrderTypeResponseType);
    }

    protected boolean hasTicketAmount(RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType) {
        if (retrieveTicketsByOrderTypeResponseType == null) {
            return false;
        }
        if (CollectionUtil.isEmpty(retrieveTicketsByOrderTypeResponseType.getTickets())) {
            return false;
        }
        BigDecimal ticketPrice = retrieveTicketsByOrderTypeResponseType.getTickets().stream().map(t -> {
            return Optional.ofNullable(t).map(TicketItemTypeVo::getAvailableAmount)
                .orElse(new BigDecimal(BigInteger.ZERO));
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (MathUtils.isGreaterThanZero(ticketPrice)) {
            return true;
        }
        return false;
    }

    /**
     * 把当前语言环境转换为支付平台能识别的语言环境
     *
     * @return 支付平台能识别的语言环境
     */
    protected String convertToPaymentLanguage(String local) {
        LanguageLocaleEnum languageLocaleEnum = LanguageUtil.getLanguageLocaleEnum(local);
        switch (languageLocaleEnum) {
            case ZH_CN:
                return "ZH";
            case EN_US:
                return "EN";
            case ZH_HK:
                return "GT";
            default:
                return "ZH";
        }
    }

    private String buildBookUrl(List<BookUrlInfo> bookUrlInfos, String urlType) {
        if (CollectionUtil.isEmpty(bookUrlInfos)) {
            return null;
        }
        if (StringUtil.isBlank(urlType)) {
            return null;
        }
        BookUrlInfo bookUrlInfo =
            bookUrlInfos.stream().filter(urlInfo -> urlInfo != null && urlType.equalsIgnoreCase(urlInfo.getUrlType()))
                .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (bookUrlInfo == null) {
            return null;
        }
        return bookUrlInfo.getUrlValue();
    }

    class PaymentNativeInfo {

        private String advanceCallback;
        private String extData;
        private String isClosePay;
        private String merchantId;
        private String orderId;
        private String requestId;
        private String payToken;

        public String getAdvanceCallback() {
            return advanceCallback;
        }

        public void setAdvanceCallback(String advanceCallback) {
            this.advanceCallback = advanceCallback;
        }

        public String getExtData() {
            return extData;
        }

        public void setExtData(String extData) {
            this.extData = extData;
        }

        public String getIsClosePay() {
            return isClosePay;
        }

        public void setIsClosePay(String isClosePay) {
            this.isClosePay = isClosePay;
        }

        public String getMerchantId() {
            return merchantId;
        }

        public void setMerchantId(String merchantId) {
            this.merchantId = merchantId;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public String getPayToken() {
            return payToken;
        }

        public void setPayToken(String payToken) {
            this.payToken = payToken;
        }
    }

    class PayRequestInfo {
        /**
         * 签名
         */
        private String sign;
        /**
         * 版本
         */
        private String version;
        /**
         * 平台代码：20：Online；21：Offline-Page；22：Offline-iFrame
         */
        private String platform;
        /**
         * 商户编号
         */
        private String merchantID;
        /**
         * 接口功能
         */
        private String interfaceName;
        /**
         * 客户端流水号
         */
        private String transactionID;

        /**
         * 支付关联号
         */
        private String externalNo;

        /**
         * 返回地址
         */
        private String returnUrl;

        /**
         * 语言
         */
        private String language;

        /**
         * 订单标题
         */
        private String orderTitleInfo;

        /**
         * 订单详情
         */
        private String orderPromptInfo;

        /**
         * 温馨提示
         */
        private String paymentDescription;

        /**
         * 上一步页面地址
         */
        private String previousStepUrl;

        /**
         * 回调发票信息地址
         */
        private String invoiceInfoUrl;

        /**
         * 商户订单号
         */
        private String orderID;

        /**
         * 担保交易金额
         */
        private String guaranteeAmount;

        /**
         * 担保交易币种
         */
        private String guaranteeCurrency;

        /**
         * 显示金额
         */
        private String amount;

        /**
         * 显示币种
         */
        private String currencyCode;

        /**
         * 支付方式折扣费扩展
         */
        private List<PayDiscountInfo> payDiscountRange;

        /**
         * 是否预授权交易
         */
        private String isNeedPreAuth;

        /**
         * 是否担保交易
         */
        private String isGuaranteePay;

        /**
         * 营销方案
         */
        private String marketingPlan;

        /**
         * 客户编号
         */
        private String customerID;

        /**
         * 客户显示姓名
         */
        private String customerName;

        /**
         * 是否允许现金余额支付
         */
        private String enableCashAccountPay;

        /**
         * 是否允许礼品卡支付
         */
        private String enableTicketPay;

        /**
         * 礼品卡类型
         */
        private String ticketType;

        /**
         * 支付类别白名单
         */
        private String enabledPayCatalog;

        /**
         * 支付方式白名单
         */
        private String enabledPayWay;

        /**
         * 支付方式黑名单
         */
        private String disabledPayWay;

        /**
         * 信用卡有效期约束
         */
        private String enabledCardExpirationDate;

        /**
         * 信用卡卡号段约束
         */
        private String creditCardNoRange;

        /**
         * （已过时）是否显示发票
         */
        private String enableInvoice;

        /**
         * 关联客户端流水号
         */
        private String relatedTransactionID;

        /**
         * 支付请求扩展
         */
        private PayExtendInfo payRequestExtend;

        /**
         * 指定持卡人
         */
        private String cardHolder;

        /**
         * 签名
         */
        private String merchantKey;

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getPlatform() {
            return platform;
        }

        public void setPlatform(String platform) {
            this.platform = platform;
        }

        public String getMerchantID() {
            return merchantID;
        }

        public void setMerchantID(String merchantID) {
            this.merchantID = merchantID;
        }

        public String getInterfaceName() {
            return interfaceName;
        }

        public void setInterfaceName(String interfaceName) {
            this.interfaceName = interfaceName;
        }

        public String getTransactionID() {
            return transactionID;
        }

        public void setTransactionID(String transactionID) {
            this.transactionID = transactionID;
        }

        public String getExternalNo() {
            return externalNo;
        }

        public void setExternalNo(String externalNo) {
            this.externalNo = externalNo;
        }

        public String getReturnUrl() {
            return returnUrl;
        }

        public void setReturnUrl(String returnUrl) {
            this.returnUrl = returnUrl;
        }

        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }

        public String getOrderTitleInfo() {
            return orderTitleInfo;
        }

        public void setOrderTitleInfo(String orderTitleInfo) {
            this.orderTitleInfo = orderTitleInfo;
        }

        public String getOrderPromptInfo() {
            return orderPromptInfo;
        }

        public void setOrderPromptInfo(String orderPromptInfo) {
            this.orderPromptInfo = orderPromptInfo;
        }

        public String getPaymentDescription() {
            return paymentDescription;
        }

        public void setPaymentDescription(String paymentDescription) {
            this.paymentDescription = paymentDescription;
        }

        public String getPreviousStepUrl() {
            return previousStepUrl;
        }

        public void setPreviousStepUrl(String previousStepUrl) {
            this.previousStepUrl = previousStepUrl;
        }

        public String getInvoiceInfoUrl() {
            return invoiceInfoUrl;
        }

        public void setInvoiceInfoUrl(String invoiceInfoUrl) {
            this.invoiceInfoUrl = invoiceInfoUrl;
        }

        public String getOrderID() {
            return orderID;
        }

        public void setOrderID(String orderID) {
            this.orderID = orderID;
        }

        public String getGuaranteeAmount() {
            return guaranteeAmount;
        }

        public void setGuaranteeAmount(String guaranteeAmount) {
            this.guaranteeAmount = guaranteeAmount;
        }

        public String getGuaranteeCurrency() {
            return guaranteeCurrency;
        }

        public void setGuaranteeCurrency(String guaranteeCurrency) {
            this.guaranteeCurrency = guaranteeCurrency;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getCurrencyCode() {
            return currencyCode;
        }

        public void setCurrencyCode(String currencyCode) {
            this.currencyCode = currencyCode;
        }

        public List<PayDiscountInfo> getPayDiscountRange() {
            return payDiscountRange;
        }

        public void setPayDiscountRange(List<PayDiscountInfo> payDiscountRange) {
            this.payDiscountRange = payDiscountRange;
        }

        public String getIsNeedPreAuth() {
            return isNeedPreAuth;
        }

        public void setIsNeedPreAuth(String isNeedPreAuth) {
            this.isNeedPreAuth = isNeedPreAuth;
        }

        public String getIsGuaranteePay() {
            return isGuaranteePay;
        }

        public void setIsGuaranteePay(String isGuaranteePay) {
            this.isGuaranteePay = isGuaranteePay;
        }

        public String getMarketingPlan() {
            return marketingPlan;
        }

        public void setMarketingPlan(String marketingPlan) {
            this.marketingPlan = marketingPlan;
        }

        public String getCustomerID() {
            return customerID;
        }

        public void setCustomerID(String customerID) {
            this.customerID = customerID;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getEnableCashAccountPay() {
            return enableCashAccountPay;
        }

        public void setEnableCashAccountPay(String enableCashAccountPay) {
            this.enableCashAccountPay = enableCashAccountPay;
        }

        public String getEnableTicketPay() {
            return enableTicketPay;
        }

        public void setEnableTicketPay(String enableTicketPay) {
            this.enableTicketPay = enableTicketPay;
        }

        public String getTicketType() {
            return ticketType;
        }

        public void setTicketType(String ticketType) {
            this.ticketType = ticketType;
        }

        public String getEnabledPayCatalog() {
            return enabledPayCatalog;
        }

        public void setEnabledPayCatalog(String enabledPayCatalog) {
            this.enabledPayCatalog = enabledPayCatalog;
        }

        public String getEnabledPayWay() {
            return enabledPayWay;
        }

        public void setEnabledPayWay(String enabledPayWay) {
            this.enabledPayWay = enabledPayWay;
        }

        public String getDisabledPayWay() {
            return disabledPayWay;
        }

        public void setDisabledPayWay(String disabledPayWay) {
            this.disabledPayWay = disabledPayWay;
        }

        public String getEnabledCardExpirationDate() {
            return enabledCardExpirationDate;
        }

        public void setEnabledCardExpirationDate(String enabledCardExpirationDate) {
            this.enabledCardExpirationDate = enabledCardExpirationDate;
        }

        public String getCreditCardNoRange() {
            return creditCardNoRange;
        }

        public void setCreditCardNoRange(String creditCardNoRange) {
            this.creditCardNoRange = creditCardNoRange;
        }

        public String getEnableInvoice() {
            return enableInvoice;
        }

        public void setEnableInvoice(String enableInvoice) {
            this.enableInvoice = enableInvoice;
        }

        public String getRelatedTransactionID() {
            return relatedTransactionID;
        }

        public void setRelatedTransactionID(String relatedTransactionID) {
            this.relatedTransactionID = relatedTransactionID;
        }

        public PayExtendInfo getPayRequestExtend() {
            return payRequestExtend;
        }

        public void setPayRequestExtend(PayExtendInfo payRequestExtend) {
            this.payRequestExtend = payRequestExtend;
        }

        public String getCardHolder() {
            return cardHolder;
        }

        public void setCardHolder(String cardHolder) {
            this.cardHolder = cardHolder;
        }

        public String getMerchantKey() {
            return merchantKey;
        }

        public void setMerchantKey(String merchantKey) {
            this.merchantKey = merchantKey;
        }
    }

    class PayExtendInfo {

        /**
         * 允许Account密码验证
         */
        private String enabledAccountPwd;

        /**
         * 支付平台收款单托管版本 G：机票过渡 T：托管版本 F：非托管版本
         */
        private String managedVersion;

        /**
         * 全额票券支付提示文字
         */
        private String fullVounchPayShowText;

        /**
         * 票券混合支付黑名单，如Cash,CCGuarantee
         */
        private String voucherMixedDisabledPayWay;

        /**
         * 限制礼品卡使用金额
         */
        public String tmLimitAmount;

        /**
         * 收款城市(不可以为空)
         */
        private String mayReceiveBranch;

        /**
         * 收款票点(默认0)
         */
        private String mayReceiveSite;

        /**
         * 现金支付是否显示地址列表，默认0不显示
         */
        private Integer hideReceiveSite;

        /**
         * 收款单支付截止日期  【暂定】形如**************，必须是14位数字，精确到秒 .ToString(“yyyyMMddHHmmss”)
         */
        private String payDeadLine;

        /**
         * 回调web服务接口名称 如：StatusReCallType
         */
        private String recallType;

        /**
         * 批次号
         */
        private String batchDocumentNo;

        /**
         * 批次类型
         */
        private String batchDocumentType;

        /**
         * 支付收集客户端回调信息
         */
        private String collectRecall;

        /**
         * 业务币种对人民币的汇率
         */
        private BigDecimal businessExchangeRate;

        /**
         * 机票接入专用  是一个Json格式串,  保证不出现 & 和 =
         */
        private String billDesc;

        /**
         * 是否需要支付平台完成抛风控的操作
         */
        private String needCallRiskControl;

        /**
         * 是否实时支付
         * （实时支付使用）0非实时；1实时；默认0
         */
        private Integer isRealTimePay;

        /**
         * 实时支付时是否自动发起扣款
         * （实时支付使用），0：PD扣款；1：支付平台扣款；默认0
         */
        private Integer isAutoApplyBill;

        /**
         * 支付提交结果通知地址
         */
        private String paymentNotify;

        /**
         * 支付提交结果通知地址
         */
        private String ivrLanguage;

        /**
         * 跳转BU完成页使用的提交方式
         */
        private String returnMethod;

        /**
         * 获取联系人信息的服务Url
         */
        private String contactsInfoUrl;
        /**
         * 获取现金收款地址的服务Url
         */
        private String paymentAddressUrl;
        /**
         * 收款方：1到携程，2到商户，缺省为1
         */
        private Integer payee;

        /**
         * 支付方式选择模式：1发卡行；2卡组织，缺省为1
         */
        private Integer paywayOptionMode;

        /**
         * 押金余额
         */
        private String depositAmount;

        /**
         * 所需积分 20150908
         */
        private String creditNeed;

        /**
         * 用户可用积分（2014-11-26）
         */
        private String credit;

        /**
         * 支票付款公司名称
         */
        private String checkPayableTo;

        /**
         * 支付时限 单位分钟，两小时表示为 120
         */
        private String payTimeLimit;

        /**
         * 韩币第三方Inicis必传字段，json格式
         * {"ShippingFullName":"","ShippingEmail":"","ShippingMobile":"","BillingEmail":""}
         * 其中各项参数需要进行UrlEncode
         */
        private String billingAddrDetail;

        /**
         * 改单模式： 1差额，2全额，缺省为1
         */
        private Integer updateBillMode;
        /**
         * 改单对应前一次支付请求的TransactionID
         */
        private String prevTransactionID;
        /**
         * 改单专用-前一次支付Bill单号
         */
        private String prevBillNo;
        /**
         * 改单专用-是否扣款沿用：0不沿用，1沿用，缺省为0
         */
        private Integer isReUseAmount;
        /**
         * 改单是否支持修改支付方式：0不支持，1支持，缺省为0
         */
        private Integer supportChangePayway;
        /**
         * 改单使用原单模式：1使用原单，2修改原单，缺省为1
         */
        private Integer usePrevPaywayMode;

        /**
         * 直连
         */
        private String directPay;

        /**
         * 上一次使用过的信用卡编号
         */
        private String oldCardInfoID;

        /**
         * 保险信息
         */
        private String insuranceInfos;

        /**
         * 是否使用app版paymentnotify契约，1使用，其他不使用
         */
        private String isAppPaymentNotify;

        /**
         * 订单支付截止时间:yyyyMMddHHmmss
         */
        private String orderAvailableTime;

        public String getEnabledAccountPwd() {
            return enabledAccountPwd;
        }

        public void setEnabledAccountPwd(String enabledAccountPwd) {
            this.enabledAccountPwd = enabledAccountPwd;
        }

        public String getManagedVersion() {
            return managedVersion;
        }

        public void setManagedVersion(String managedVersion) {
            this.managedVersion = managedVersion;
        }

        public String getFullVounchPayShowText() {
            return fullVounchPayShowText;
        }

        public void setFullVounchPayShowText(String fullVounchPayShowText) {
            this.fullVounchPayShowText = fullVounchPayShowText;
        }

        public String getVoucherMixedDisabledPayWay() {
            return voucherMixedDisabledPayWay;
        }

        public void setVoucherMixedDisabledPayWay(String voucherMixedDisabledPayWay) {
            this.voucherMixedDisabledPayWay = voucherMixedDisabledPayWay;
        }

        public String getTmLimitAmount() {
            return tmLimitAmount;
        }

        public void setTmLimitAmount(String tmLimitAmount) {
            this.tmLimitAmount = tmLimitAmount;
        }

        public String getMayReceiveBranch() {
            return mayReceiveBranch;
        }

        public void setMayReceiveBranch(String mayReceiveBranch) {
            this.mayReceiveBranch = mayReceiveBranch;
        }

        public String getMayReceiveSite() {
            return mayReceiveSite;
        }

        public void setMayReceiveSite(String mayReceiveSite) {
            this.mayReceiveSite = mayReceiveSite;
        }

        public Integer getHideReceiveSite() {
            return hideReceiveSite;
        }

        public void setHideReceiveSite(Integer hideReceiveSite) {
            this.hideReceiveSite = hideReceiveSite;
        }

        public String getPayDeadLine() {
            return payDeadLine;
        }

        public void setPayDeadLine(String payDeadLine) {
            this.payDeadLine = payDeadLine;
        }

        public String getRecallType() {
            return recallType;
        }

        public void setRecallType(String recallType) {
            this.recallType = recallType;
        }

        public String getBatchDocumentNo() {
            return batchDocumentNo;
        }

        public void setBatchDocumentNo(String batchDocumentNo) {
            this.batchDocumentNo = batchDocumentNo;
        }

        public String getBatchDocumentType() {
            return batchDocumentType;
        }

        public void setBatchDocumentType(String batchDocumentType) {
            this.batchDocumentType = batchDocumentType;
        }

        public String getCollectRecall() {
            return collectRecall;
        }

        public void setCollectRecall(String collectRecall) {
            this.collectRecall = collectRecall;
        }

        public BigDecimal getBusinessExchangeRate() {
            return businessExchangeRate;
        }

        public void setBusinessExchangeRate(BigDecimal businessExchangeRate) {
            this.businessExchangeRate = businessExchangeRate;
        }

        public String getBillDesc() {
            return billDesc;
        }

        public void setBillDesc(String billDesc) {
            this.billDesc = billDesc;
        }

        public String getNeedCallRiskControl() {
            return needCallRiskControl;
        }

        public void setNeedCallRiskControl(String needCallRiskControl) {
            this.needCallRiskControl = needCallRiskControl;
        }

        public Integer getIsRealTimePay() {
            return isRealTimePay;
        }

        public void setIsRealTimePay(Integer isRealTimePay) {
            this.isRealTimePay = isRealTimePay;
        }

        public Integer getIsAutoApplyBill() {
            return isAutoApplyBill;
        }

        public void setIsAutoApplyBill(Integer isAutoApplyBill) {
            this.isAutoApplyBill = isAutoApplyBill;
        }

        public String getPaymentNotify() {
            return paymentNotify;
        }

        public void setPaymentNotify(String paymentNotify) {
            this.paymentNotify = paymentNotify;
        }

        public String getIvrLanguage() {
            return ivrLanguage;
        }

        public void setIvrLanguage(String ivrLanguage) {
            this.ivrLanguage = ivrLanguage;
        }

        public String getReturnMethod() {
            return returnMethod;
        }

        public void setReturnMethod(String returnMethod) {
            this.returnMethod = returnMethod;
        }

        public String getContactsInfoUrl() {
            return contactsInfoUrl;
        }

        public void setContactsInfoUrl(String contactsInfoUrl) {
            this.contactsInfoUrl = contactsInfoUrl;
        }

        public String getPaymentAddressUrl() {
            return paymentAddressUrl;
        }

        public void setPaymentAddressUrl(String paymentAddressUrl) {
            this.paymentAddressUrl = paymentAddressUrl;
        }

        public Integer getPayee() {
            return payee;
        }

        public void setPayee(Integer payee) {
            this.payee = payee;
        }

        public Integer getPaywayOptionMode() {
            return paywayOptionMode;
        }

        public void setPaywayOptionMode(Integer paywayOptionMode) {
            this.paywayOptionMode = paywayOptionMode;
        }

        public String getDepositAmount() {
            return depositAmount;
        }

        public void setDepositAmount(String depositAmount) {
            this.depositAmount = depositAmount;
        }

        public String getCreditNeed() {
            return creditNeed;
        }

        public void setCreditNeed(String creditNeed) {
            this.creditNeed = creditNeed;
        }

        public String getCredit() {
            return credit;
        }

        public void setCredit(String credit) {
            this.credit = credit;
        }

        public String getCheckPayableTo() {
            return checkPayableTo;
        }

        public void setCheckPayableTo(String checkPayableTo) {
            this.checkPayableTo = checkPayableTo;
        }

        public String getPayTimeLimit() {
            return payTimeLimit;
        }

        public void setPayTimeLimit(String payTimeLimit) {
            this.payTimeLimit = payTimeLimit;
        }

        public String getBillingAddrDetail() {
            return billingAddrDetail;
        }

        public void setBillingAddrDetail(String billingAddrDetail) {
            this.billingAddrDetail = billingAddrDetail;
        }

        public Integer getUpdateBillMode() {
            return updateBillMode;
        }

        public void setUpdateBillMode(Integer updateBillMode) {
            this.updateBillMode = updateBillMode;
        }

        public String getPrevTransactionID() {
            return prevTransactionID;
        }

        public void setPrevTransactionID(String prevTransactionID) {
            this.prevTransactionID = prevTransactionID;
        }

        public String getPrevBillNo() {
            return prevBillNo;
        }

        public void setPrevBillNo(String prevBillNo) {
            this.prevBillNo = prevBillNo;
        }

        public Integer getIsReUseAmount() {
            return isReUseAmount;
        }

        public void setIsReUseAmount(Integer isReUseAmount) {
            this.isReUseAmount = isReUseAmount;
        }

        public Integer getSupportChangePayway() {
            return supportChangePayway;
        }

        public void setSupportChangePayway(Integer supportChangePayway) {
            this.supportChangePayway = supportChangePayway;
        }

        public Integer getUsePrevPaywayMode() {
            return usePrevPaywayMode;
        }

        public void setUsePrevPaywayMode(Integer usePrevPaywayMode) {
            this.usePrevPaywayMode = usePrevPaywayMode;
        }

        public String getDirectPay() {
            return directPay;
        }

        public void setDirectPay(String directPay) {
            this.directPay = directPay;
        }

        public String getOldCardInfoID() {
            return oldCardInfoID;
        }

        public void setOldCardInfoID(String oldCardInfoID) {
            this.oldCardInfoID = oldCardInfoID;
        }

        public String getInsuranceInfos() {
            return insuranceInfos;
        }

        public void setInsuranceInfos(String insuranceInfos) {
            this.insuranceInfos = insuranceInfos;
        }

        public String getIsAppPaymentNotify() {
            return isAppPaymentNotify;
        }

        public void setIsAppPaymentNotify(String isAppPaymentNotify) {
            this.isAppPaymentNotify = isAppPaymentNotify;
        }

        public String getOrderAvailableTime() {
            return orderAvailableTime;
        }

        public void setOrderAvailableTime(String orderAvailableTime) {
            this.orderAvailableTime = orderAvailableTime;
        }
    }

    class PayDiscountInfo {
        /**
         * 折扣/立减金额
         */
        private BigDecimal discount;
        /**
         * 折扣/立减名称
         */
        private String discountName;
        /**
         * 立减模式; 0：单一支付方式立减，1：组合支付方式立减
         */
        private int mode;
        /**
         * 支付方式编码
         */
        private String payWayID;

        private String description;

        public BigDecimal getDiscount() {
            return discount;
        }

        public void setDiscount(BigDecimal discount) {
            this.discount = discount;
        }

        public String getDiscountName() {
            return discountName;
        }

        public void setDiscountName(String discountName) {
            this.discountName = discountName;
        }

        public int getMode() {
            return mode;
        }

        public void setMode(int mode) {
            this.mode = mode;
        }

        public String getPayWayID() {
            return payWayID;
        }

        public void setPayWayID(String payWayID) {
            this.payWayID = payWayID;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

}
