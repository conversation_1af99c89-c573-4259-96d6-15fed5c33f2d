package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;

import com.ctrip.corp.bff.framework.hotel.common.constant.AccountInfoConstant;
import com.ctrip.corp.bff.framework.hotel.common.util.TimeUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ArriveTimeInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ArriveTimeToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple12;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple14;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.signature.param.BookInfoBO;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfParamValid;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.QconfigEntityOfGroupHotelMemberCardRule;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchResponseType;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckResponseType;
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoResponseType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.PersonAccountUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.contract.HotelContactorInfo;
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.PointsInfo;
import com.ctrip.corp.bff.hotel.book.contract.TeamRoomInfo;
import com.ctrip.soa._21234.BasicInfo;
import com.ctrip.soa._21234.SearchTripDetailResponseType;

import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import org.apache.commons.collections.MapUtils;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 20:12
 * 依赖外部接口/token等 参数合理性校验
 * 校验-同行程校验开关变更不可下单
 */
@Component public class MapperOfParamValidResponse extends
    AbstractMapper<Tuple1<WrapperOfParamValid>, Boolean> {
    private static final Integer MEMBERSHIP_CARD_MAX_LENGTH = 21;
    private static final int MEMBER_PARENT_NUMBER_END = 57;
    private static final int MEMBER_PARENT_NUMBER_START = 48;
    private static final int MEMBER_PARENT_UPPERCASE_LETTERS_START = 65;
    private static final int MEMBER_PARENT_UPPERCASE_LETTERS_END = 90;
    private static final int MEMBER_PARENT_LOWERCASE_LETTERS_START = 97;
    private static final int MEMBER_PARENT_LOWERCASE_LETTERS_END = 122;
    private static final int SHARE_ROOM_MAX_PSG = 3;
    private static final int SHARE_ROOM_MAX_ROOM = 1;
    /**
     * 配置是否要检验 因私三方协议酒店入住人必须包含卡主本人
     */
    private static final String CHECK_THIRD_PARTY_AGREEMENT_PSG_QCONFIG_KEY = "checkThirdPartyAgreementPsg";

    private static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";

    // 提前审批大开关开启
    private static final String IS_CHKAHEAD_APPROVE_STATUS_T = "T";
    private static final String CHECK_FAIL_CODE = "500";

    @Override protected Boolean convert(
        Tuple1<WrapperOfParamValid> tuple) {
        return true;
    }

    @Override protected ParamCheckResult check(
        Tuple1<WrapperOfParamValid> tuple) {
        WrapperOfParamValid wrapperOfParamValid = tuple.getT1();
        OrderCreateRequestType orderCreateRequestType = wrapperOfParamValid.getOrderCreateRequestType();
        ResourceToken resourceToken = wrapperOfParamValid.getResourceToken();
        AllocationResultToken costAllocationToken = wrapperOfParamValid.getAllocationResultToken();
        OrderCreateToken orderCreateToken = wrapperOfParamValid.getOrderCreateToken();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfParamValid.getAccountInfo();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType =
            wrapperOfParamValid.getQueryCheckAvailContextResponseType();
        SearchTripDetailResponseType searchTripDetailResponseType =
            wrapperOfParamValid.getSearchTripDetailResponseType();
        QueryIndividualAccountResponseType queryIndividualAccountResponseType =
            wrapperOfParamValid.getQueryIndividualAccountResponseType();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = wrapperOfParamValid.getBaseCheckAvailInfo();
        List<MemberBonusRuleEntry> qconfigEntityOfGroupHotelMemberCardRule =
            wrapperOfParamValid.getMemberBonusRuleEntries();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig =
            wrapperOfParamValid.getQconfigOfCertificateInitConfig();
        ExternalDataCheckResponseType externalDataCheckResponseType = wrapperOfParamValid.getExternalDataCheckResponseType();
        CustomConfigSearchResponseType customConfigSearchResponseType = wrapperOfParamValid.getCustomConfigSearchResponseType();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfParamValid.getStrategyInfoMap();
        // 腾讯员工号校验
        externalDataCheck(externalDataCheckResponseType, customConfigSearchResponseType, orderCreateRequestType);
        // 校验入参能否跟base匹配上
        checkBookParams(orderCreateRequestType, resourceToken, queryCheckAvailContextResponseType);
        // 万豪直连限定非员工不可入住
        whOnlyCorp(checkAvailInfo, orderCreateRequestType);
        // 校验-审批单号
        checkApprovalInput(orderCreateRequestType, accountInfo, wrapperOfParamValid.getStrategyInfoMap());

        // 依赖的token和接口响应的参数校验
        if (CorpPayInfoUtil.isPublic(orderCreateRequestType.getCorpPayInfo()) &&
            StringUtil.isBlank(resourceToken.getReservationResourceToken().getPolicyToken())) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR,
                OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR.getErrorCode().toString());
        }
        if (queryCheckAvailContextResponseType == null || queryCheckAvailContextResponseType.getRoomInfo() == null) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.VALID_QUERY_CHECK_AVAIL_CONTEXT_ERROR,
                OrderCreateErrorEnum.VALID_QUERY_CHECK_AVAIL_CONTEXT_ERROR.getErrorCode().toString());
        }
        // 入参校验
        ParamCheckResult paramCheckResult;
        // 校验-校验二次提交关键信息
        paramCheckResult = checkContinueCommitInfo(orderCreateRequestType, orderCreateToken);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-入住人相关信息
        paramCheckResult =
            checkClientPsg(orderCreateRequestType, accountInfo, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 可定反查的房间数
        paramCheckResult = checkCheckAvailContextResult(orderCreateRequestType, queryCheckAvailContextResponseType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-个人账户信息开关变更不可下单
        paramCheckResult = checkPersonalAccount(orderCreateRequestType, queryIndividualAccountResponseType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-阿斯利康行程仅支持为政策执行人自己预订
        paramCheckResult = checkTripClientPsg(orderCreateRequestType, searchTripDetailResponseType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-行程政策执行人必须为下单政策执行人
        paramCheckResult = checkOrderTripPolicyId(orderCreateRequestType, searchTripDetailResponseType, accountInfo);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-福利房必须包含本人
        paramCheckResult = checkWelfareRoomClientPsg(orderCreateRequestType, queryCheckAvailContextResponseType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-出行人模式入住人必须有房间号
        paramCheckResult = checkTravelPolicy(orderCreateRequestType, accountInfo);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-紧急预订场景，秘书为老板预订不可含本人
        paramCheckResult = checkBookingForBoss(orderCreateRequestType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-会员积分勾选时入住人手机号必须包含联系人手机号
        paramCheckResult = checkEarnPointsClientPsg(orderCreateRequestType, queryCheckAvailContextResponseType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-双拼同住代订相关验证
        paramCheckResult = checkDuplexModel(orderCreateRequestType, accountInfo);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-会员卡格式
        paramCheckResult = checkMembershipCardNumFormat(orderCreateRequestType, queryCheckAvailContextResponseType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-因私三方协议酒店, 入住人人必须包含卡主本人
        paramCheckResult = checkThirdPartyAgreementPsg(orderCreateRequestType, queryCheckAvailContextResponseType);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-分摊总金额校验
        paramCheckResult = checkCostAllocationResult(costAllocationToken);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }

        // 校验-amadesu酒店集团会员卡填写信息校验
        paramCheckResult = checkAmadeusCardNo(orderCreateRequestType, queryCheckAvailContextResponseType,
            qconfigEntityOfGroupHotelMemberCardRule);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 校验-非同行程入住人审批单号 checkPassengerApprovalInfo(orderCreateRequestType, accountInfo, generalBatchSearchAccountInfoResponseType);
        return paramCheckResult;
    }

    protected void externalDataCheck(ExternalDataCheckResponseType externalDataCheckResponseType,
        CustomConfigSearchResponseType customConfigSearchResponseType, OrderCreateRequestType orderCreateRequestType) {
        if (!OrderCreateProcessorOfUtil.needExternalEmployeeId(customConfigSearchResponseType,
            orderCreateRequestType.getIntegrationSoaRequestType())) {
            return;
        }
        if (externalDataCheckResponseType == null || CollectionUtil.isEmpty(
            externalDataCheckResponseType.getCheckResultList()) || externalDataCheckResponseType.getCheckResultList()
            .stream()
            .anyMatch(checkResult -> checkResult != null && CHECK_FAIL_CODE.equalsIgnoreCase(checkResult.getCode()))) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PSG_EXTERNAL_EMPLOYEE_ID_ERROR,
                OrderCreateErrorEnum.PSG_EXTERNAL_EMPLOYEE_ID_ERROR.getErrorMessage());
        }
        if (externalDataCheckResponseType.getCheckResultList().stream().anyMatch(
            checkResult -> checkResult == null || !BooleanUtils.isTrue(
                checkResult.isResult()))) {
            List<String> errorDataValue = new ArrayList<>();
            externalDataCheckResponseType.getCheckResultList().forEach(checkResult -> {
                if (checkResult != null && !BooleanUtils.isTrue(checkResult.isResult())
                    && StringUtil.isNotBlank(checkResult.getDataValue())) {
                    errorDataValue.add(checkResult.getDataValue());
                }
            });
            if (CollectionUtil.isEmpty(errorDataValue)) {
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PSG_EXTERNAL_EMPLOYEE_ID_ERROR,
                    OrderCreateErrorEnum.PSG_EXTERNAL_EMPLOYEE_ID_ERROR.getErrorMessage());
            }
            String friendlyMessage = OrderCreateErrorEnum.EXTERNAL_EMPLOYEE_ID_ERROR.getFriendlyMessage();
            if (StringUtil.isBlank(friendlyMessage)) {
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PSG_EXTERNAL_EMPLOYEE_ID_ERROR,
                    OrderCreateErrorEnum.PSG_EXTERNAL_EMPLOYEE_ID_ERROR.getErrorMessage());
            }
            throw BusinessExceptionBuilder.createAlertException(
                OrderCreateErrorEnum.EXTERNAL_EMPLOYEE_ID_ERROR.getErrorCode(),
                OrderCreateErrorEnum.EXTERNAL_EMPLOYEE_ID_ERROR.getErrorMessage(),
                String.format(friendlyMessage, String.join("、", errorDataValue)),
                OrderCreateErrorEnum.EXTERNAL_EMPLOYEE_ID_ERROR.getErrorMessage());
        }
    }

    /**
     * 校验-人下的审批单信息
     * 同行程，所有人都需要审批单----在MapperOfVerifyFellowPassengerRequestType中校验
     * 非同行程，按出行人子账户提前审批开关读取，入住人子账户开了提前审批需要审批单/紧急预订/仅查询，否则不需要，非员工读登录卡的配置
     * 非同行程，非按出行人子账户提前审批开关读取，所有人都需要审批单/紧急预订/仅查询
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @param generalBatchSearchAccountInfoResponseType
     */
    protected void checkPassengerApprovalInfo(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo,
        GeneralBatchSearchAccountInfoResponseType generalBatchSearchAccountInfoResponseType) {
        // 非同行程，按出行人子账户提前审批开关读取，入住人子账户开了提前审批需要审批单/紧急预订/仅查询，否则不需要，非员工读登录卡的配置
        checkPassengerApprovalInfoByPassenger(accountInfo, orderCreateRequestType,
            generalBatchSearchAccountInfoResponseType);
        // 非同行程，非按出行人子账户提前审批开关读取，所有人都需要审批单/紧急预订/仅查询
        checkPassengerApprovalInfoByLoginUid(accountInfo, orderCreateRequestType);
    }

    /**
     * 审批单按人选--非同行程，非按出行人子账户提前审批开关读取，所有人都需要审批单/紧急预订/仅查询
     *
     * @param accountInfo
     * @param orderCreateRequestType
     */
    protected void checkPassengerApprovalInfoByLoginUid(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return;
        }
        if (!accountInfo.preApprovalChooseByPsg(CityInfoUtil.oversea(
                Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null)),
            orderCreateRequestType.getCorpPayInfo())) {
            return;
        }
        if (accountInfo.preApprovalSameTrip(CityInfoUtil.oversea(
                Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null)),
            orderCreateRequestType.getCorpPayInfo())) {
            return;
        }
        if (accountInfo.preApprovalStrategyByPsg(CityInfoUtil.oversea(
                Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null)),
            orderCreateRequestType.getCorpPayInfo())) {
            return;
        }
        orderCreateRequestType.getHotelBookPassengerInputs().stream().forEach(hotelPassenger -> {
            if (hotelPassenger == null) {
                return;
            }
            if (buildHasApprovalInfo(hotelPassenger)) {
                return;
            }
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO,
                OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO.getErrorMessage());
        });
    }

    /**
     * 审批单按人选--按出行人子账户提前审批开关读取，入住人子账户开了提前审批需要审批单/紧急预订/仅查询，否则不需要，非员工读登录卡的配置
     *
     * @param accountInfo
     * @param orderCreateRequestType
     * @param generalBatchSearchAccountInfoResponseType
     */
    protected void checkPassengerApprovalInfoByPassenger(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType,
        GeneralBatchSearchAccountInfoResponseType generalBatchSearchAccountInfoResponseType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return;
        }
        if (!accountInfo.preApprovalStrategyByPsg(CityInfoUtil.oversea(
                Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null)),
            orderCreateRequestType.getCorpPayInfo())) {
            return;
        }
        Map<String, Boolean> preApprovalMap =
            buildPreApprovalMap(generalBatchSearchAccountInfoResponseType, orderCreateRequestType);
        orderCreateRequestType.getHotelBookPassengerInputs().stream().forEach(hotelBookPassengerInput -> {
            if (hotelBookPassengerInput == null) {
                return;
            }
            if (buildHasApprovalInfo(hotelBookPassengerInput)) {
                return;
            }
            // 员工 读取出行人子账户的提前审批开关 非员工 读取登录卡的提前审批开关
            String useId = OrderCreateProcessorOfUtil.buildEmployee(hotelBookPassengerInput) ?
                hotelBookPassengerInput.getHotelPassengerInput().getUid() :
                hotelBookPassengerInput.getHotelPassengerInput().getInfoId();
            if (BooleanUtil.isTrue(preApprovalMap.get(useId))) {
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO,
                    OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO.getErrorMessage());
            }
        });
    }

    /**
     * 入住人的提前审批大开关配置
     *
     * @param generalBatchSearchAccountInfoResponseType
     * @return
     */
    protected Map<String, Boolean> buildPreApprovalMap(
        GeneralBatchSearchAccountInfoResponseType generalBatchSearchAccountInfoResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        if (generalBatchSearchAccountInfoResponseType == null || CollectionUtil.isEmpty(
            generalBatchSearchAccountInfoResponseType.getResults())) {
            return new HashMap<>();
        }
        Map<String, Boolean> preApprovalMap = new HashMap<>();
        boolean oversea = CityInfoUtil.oversea(
            Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null));
        generalBatchSearchAccountInfoResponseType.getResults().forEach(accountInfo -> {
            if (accountInfo == null || StringUtil.isBlank(accountInfo.getUid())) {
                return;
            }
            if (CollectionUtil.isEmpty(accountInfo.getResults())) {
                return;
            }
            if (oversea) {
                preApprovalMap.put(accountInfo.getUid(), IS_CHKAHEAD_APPROVE_STATUS_T.equalsIgnoreCase(
                    accountInfo.getResults().get(AccountInfoConstant.ACC_FIELD_ISCHKAHEADAPPROVEI)));
            } else {
                preApprovalMap.put(accountInfo.getUid(), IS_CHKAHEAD_APPROVE_STATUS_T.equalsIgnoreCase(
                    accountInfo.getResults().get(AccountInfoConstant.ACC_FIELD_ISCHKAHEADAPPROVE)));
            }
        });
        return preApprovalMap;
    }

    /**
     * 入住人是否有审批信息：紧急预订/选了审批单号
     *
     * @param hotelBookPassengerInput
     * @return
     */
    protected boolean buildHasApprovalInfo(HotelBookPassengerInput hotelBookPassengerInput) {
        if (hotelBookPassengerInput == null || hotelBookPassengerInput.getHotelPassengerInput() == null
            || hotelBookPassengerInput.getHotelPassengerInput().getApprovalInput() == null) {
            return false;
        }
        if (StringUtil.isNotBlank(
            hotelBookPassengerInput.getHotelPassengerInput().getApprovalInput().getSubApprovalNo())) {
            return true;
        }
        if (BooleanUtil.parseStr(true)
            .equalsIgnoreCase(hotelBookPassengerInput.getHotelPassengerInput().getApprovalInput().getEmergency())) {
            return true;
        }
        return false;
    }

    protected boolean checkApprovalInput(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, Map<String, StrategyInfo> strategyInfoMap) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return true;
        }
        if (StringUtil.isNotBlank(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                .orElse(null))) {
            return true;
        }
        if (BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getEmergency)
                .orElse(null))) {
            return true;
        }
        if (!accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(
                Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null)),
            orderCreateRequestType.getCorpPayInfo())) {
            return true;
        }
        if (accountInfo.isOaApprovalHead() && !StrategyOfBookingInitUtil.preApprovalSupportSwitchingSubApprovalNo(
            strategyInfoMap)) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.MISS_SUBAPPROVALNO,
                OrderCreateErrorEnum.MISS_SUBAPPROVALNO.getErrorMessage());
        }
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (!Arrays.asList(HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.MIX_PAY, HotelPayTypeEnum.FLASH_STAY_PAY)
            .contains(roomPayType)) {
            return true;
        }
        throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.MISS_SUBAPPROVALNO_POST,
            OrderCreateErrorEnum.MISS_SUBAPPROVALNO_POST.getErrorMessage());
    }

    /**
     * 校验入参能否跟base匹配上
     *
     * @param orderCreateRequestType
     * @param resourceToken
     */
    private void checkBookParams(OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        try {
            // 到店时间校验--依赖token
            checkArriveTime(orderCreateRequestType, resourceToken);
            // 入离店时间校验--依赖可定反查
            checkCheckInCheckOut(orderCreateRequestType, queryCheckAvailContextResponseType);
            // 关键参数校验
            checkBookInfoBO(orderCreateRequestType, resourceToken);
        } catch (BusinessException e) {
            if (QConfigOfCustomConfig.isSupport("useCheckBookParams", orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
                throw e;
            }
            // 记录日志, 上线后优先分析，没问题再开使用开关,所以先记录info日志
            LogUtil.loggingClogOnly(LogLevelEnum.Info, MapperOfParamValidResponse.class, "checkBookParams", e, null);
        }
    }

    protected boolean checkBookInfoBO(OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        if (resourceToken.getBookResourceToken() == null) {
            return true;
        }
        // 如果BookInfoBO校验参数有改动 那么 老->新 新->老 版本都对不上
        if (!StringUtil.equalsIgnoreCase(resourceToken.getBookResourceToken().getBookVersion(),
            BookInfoBO.BOOK_VERSION)) {
            return true;
        }
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        HotelPayTypeEnum servicePayType = buildServicePayType(orderCreateRequestType, resourceToken);
        // 创单环节只有一个RoomPayType对标HotelPayTypeEnum 包含所有支付方式，未将担保单独拆分出去
        BookInfoBO bookInfoBO = OrderCreateProcessorOfUtil.buildBookInfoBO(roomPayType, servicePayType);
        String orderCreateBookInfoStr = OrderCreateProcessorOfUtil.buildSignatureBookInfo(bookInfoBO);
        if (StringUtil.isBlank(orderCreateBookInfoStr) || StringUtil.isBlank(
            resourceToken.getBookResourceToken().getBookInfoStr())) {
            return true;
        }
        if (!StringUtil.equalsIgnoreCase(orderCreateBookInfoStr,
            resourceToken.getBookResourceToken().getBookInfoStr())) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.INVALID_BOOK_INFO,
                OrderCreateErrorEnum.INVALID_BOOK_INFO.getErrorMessage());
        }
        return true;
    }

    protected HotelPayTypeEnum buildServicePayType(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken) {
        HotelPayTypeEnum servicePayType =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        // 公务卡场景 base处理成了公帐 创单也必须转成公帐做参数比对
        if (servicePayType == HotelPayTypeEnum.CORPORATE_CARD_PAY) {
            return HotelPayTypeEnum.CORP_PAY;
        }
        return servicePayType;
    }

    protected boolean checkArriveTime(OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        String baseArriveTime = Optional.ofNullable(resourceToken.getReservationResourceToken())
            .map(ReservationResourceToken::getArriveTimeKey).filter(StringUtil::isNotBlank)
            .map(this::getArriveTimeToken).map(ArriveTimeToken::getArriveTimeUTC).orElse(null);
        String arriveTime =
            Optional.ofNullable(orderCreateRequestType.getArriveTimeInput()).map(ArriveTimeInput::getArriveTimeToken)
                .filter(StringUtil::isNotBlank).map(this::getArriveTimeToken).map(ArriveTimeToken::getArriveTimeUTC)
                .orElse(null);
        if (StringUtil.isNotBlank(baseArriveTime) && StringUtil.isNotBlank(arriveTime) && !StringUtil.equalsIgnoreCase(
            baseArriveTime, arriveTime)) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.INVALID_BOOK_INFO,
                OrderCreateErrorEnum.INVALID_BOOK_INFO.getErrorMessage());
        }
        return true;
    }

    private ArriveTimeToken getArriveTimeToken(String arriveTimeToken) {
        if (StringUtil.isBlank(arriveTimeToken)) {
            return null;
        }
        return TokenParseUtil.parseToken(arriveTimeToken, ArriveTimeToken.class);
    }

    protected boolean checkCheckInCheckOut(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (queryCheckAvailContextResponseType == null || queryCheckAvailContextResponseType.getBaseInfo() == null) {
            return true;
        }
        String startTime = queryCheckAvailContextResponseType.getBaseInfo().getStartTime();
        String endTime = queryCheckAvailContextResponseType.getBaseInfo().getEndTime();
        if (StringUtil.isBlank(startTime) || StringUtil.isBlank(endTime)) {
            return true;
        }
        String checkAvailCheckIn = convertToDate(startTime);
        String checkAvailCheckOut = convertToDate(endTime);
        if (StringUtil.isBlank(checkAvailCheckIn) || StringUtil.isBlank(checkAvailCheckOut)) {
            return true;
        }
        if (checkAvailCheckIn.equalsIgnoreCase(
            orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn())
            && checkAvailCheckOut.equalsIgnoreCase(
            orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut())) {
            return true;
        }
        throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.INVALID_BOOK_INFO,
            OrderCreateErrorEnum.INVALID_BOOK_INFO.getErrorMessage());
    }

    /**
     * 注意：目前是为了校验前端入参由agg推荐的方式这么转回来，其他地方不要使用
     * agg返回格式：2025-03-06T00:00:00+08
     * 代征宇：推荐直接把+08截取掉之后再转换成LocalDate，因为这个+08实际是错误数据
     * @param dateTimeStr 2025-03-06T00:00:00+08
     * @return 2025-03-06
     */
    private String convertToDate(String dateTimeStr) {
        if (StringUtil.isBlank(dateTimeStr)) {
            return null;
        }
        String modifiedDateTimeStr = dateTimeStr.substring(0, dateTimeStr.lastIndexOf('+'));
        return TimeUtil.convertTimeStrFormat(modifiedDateTimeStr, YYYY_MM_DD_T_HH_MM_SS, TimeUtil.YYYY_MM_DD);
    }

    /**
     * 万豪直连房型不允许帮非员工代订
     */
    public void whOnlyCorp(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        OrderCreateRequestType orderCreateRequestType) {
        Boolean onlyEmployeeBooking = checkAvailInfo.isOnlyEmployeeBooking();
        // 万豪直连，且三方协议时需要处理，否则不需要拦截
        if (org.apache.commons.lang.BooleanUtils.isNotTrue(onlyEmployeeBooking)) {
            return;
        }
        List<HotelBookPassengerInput> hotelBookPassengerInputs =
            Optional.ofNullable(orderCreateRequestType.getHotelBookPassengerInputs()).orElse(new ArrayList<>());
        // 是否存在非员工出行人
        boolean hasNoCorp = hotelBookPassengerInputs.stream().anyMatch(passenger -> !StringUtil.equalsIgnoreCase(
            Optional.ofNullable(passenger.getHotelPassengerInput()).map(HotelPassengerInput::getEmployee).orElse(null),
            "T"));
        if (hasNoCorp) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.ONLY_EMPLOYEE_BOOKING,
                OrderCreateErrorEnum.ONLY_EMPLOYEE_BOOKING.getErrorMessage());
        }
    }

    protected ParamCheckResult checkAmadeusCardNo(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
                                                  List<MemberBonusRuleEntry> qconfigEntityOfGroupHotelMemberCardRule) {
        if (BooleanUtils.isTrue(Optional.ofNullable(queryCheckAvailContextResponseType)
                .map(QueryCheckAvailContextResponseType::getRoomInfo).map(BookRoomInfoEntity::isOnlyGroupMemberCanBook).orElse(false)) &&
                StringUtil.isBlank(Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getMembershipInfo).map(MembershipInfo::getMembershipNo).orElse(null))) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.MEMBERSHIPNO_IS_BLANK,
                    OrderCreateErrorEnum.MEMBERSHIPNO_IS_BLANK.getErrorCode().toString());
        }
        if (queryCheckAvailContextResponseType == null || queryCheckAvailContextResponseType.getHotelInfo() == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(qconfigEntityOfGroupHotelMemberCardRule)) {
            return null;
        }
        String hotelVipCardID = Optional.ofNullable(orderCreateRequestType.getMembershipInfo())
            .map(MembershipInfo::getMembershipNo).orElse("");
        Integer hotelGroupID = queryCheckAvailContextResponseType.getHotelInfo().getHotelGroupId();
        MemberBonusRuleEntry memberBonusRuleEntry = CollectionUtil.findFirst(qconfigEntityOfGroupHotelMemberCardRule, t -> Objects.nonNull(t)
                && String.valueOf(hotelGroupID).equalsIgnoreCase(t.getGroupId()));

        if (null == memberBonusRuleEntry || StringUtil.isBlank(hotelVipCardID)) {
            return null;
        }
        String inputPattern = memberBonusRuleEntry.getInputPattern();
        Integer minLength = memberBonusRuleEntry.getMinLength();
        Integer maxLength = memberBonusRuleEntry.getMaxLength();
        String friendlyMessage = buildMessage(inputPattern, minLength, maxLength);

        if (MathUtils.isGreaterThanZero(minLength) && hotelVipCardID.length() < minLength) {
            return new ParamCheckResult(false,
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode()
                            .toString(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorMessage(),
                    friendlyMessage);
        }
        if (MathUtils.isGreaterThanZero(maxLength) && hotelVipCardID.length() > maxLength) {
            return new ParamCheckResult(false,
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode()
                            .toString(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorMessage(),
                    friendlyMessage);
        }
        if (StringUtil.equalsIgnoreCase(NUMBER, inputPattern) && !hotelVipCardID.matches("[0-9]+")) {
            return new ParamCheckResult(false,
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode()
                            .toString(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorMessage(),
                    friendlyMessage);
        }
        if (StringUtil.equalsIgnoreCase(NUMBER_AND_LETTER, inputPattern) && !hotelVipCardID.matches("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]+$")) {
            return new ParamCheckResult(false,
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorCode()
                            .toString(),
                    OrderCreateErrorEnum.PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR.getErrorMessage(),
                    friendlyMessage);
        }
        return null;
    }

    private static final String NUMBER = "NUMBER";

    private static final String NUMBER_AND_LETTER = "NUMBER_AND_LETTER";

    /**
     * 会员卡号由【{0}】位数字组成，请输入正确卡号
     */
    private static final String MEMBER_CARD_NUMBER_FIXED_CHECK_FAIL = "com.ctrip.ct.groupvipcardbind.cardnumberfixedcheckfail";

    /**
     * 会员卡号由【{0}-{1}】位数字组成，请输入正确卡号
     */
    private static final String MEMBER_CARD_NUMBER_RANGE_CHECK_FAIL = "com.ctrip.ct.groupvipcardbind.cardnumberrangecheckfail";

    /**
     * 会员卡号由【{0}】位数字和字母共同组成，请输入正确卡号
     */
    private static final String MEMBER_CARD_NUMBER_LETTER_FIXED_CHECK_FAIL = "com.ctrip.ct.groupvipcardbind.cardnumberletterfixedcheckfail";

    /**
     * 会员卡号由【{0}-{1}】位数字和字母共同组成，请输入正确卡号
     */
    private static final String MEMBER_CARD_NUMBER_LETTER_RANGE_CHECK_FAIL = "com.ctrip.ct.groupvipcardbind.cardnumberletterrangecheckfail";


    private String buildMessage(String inputPattern, Integer minLength, Integer maxLength) {
        if (StringUtil.equalsIgnoreCase(inputPattern, NUMBER)) {
            if (equals(minLength, maxLength) || (minLength == null && maxLength != null) || (minLength != null && maxLength == null)) {
                return BFFSharkUtil.getSharkValueFormat(MEMBER_CARD_NUMBER_FIXED_CHECK_FAIL,
                        minLength != null ? String.valueOf(minLength) : String.valueOf(maxLength));
            } else {
                return BFFSharkUtil.getSharkValueFormat(MEMBER_CARD_NUMBER_RANGE_CHECK_FAIL,
                        String.valueOf(minLength), String.valueOf(maxLength));
            }
        }
        if (StringUtil.equalsIgnoreCase(inputPattern, NUMBER_AND_LETTER)) {
            if (equals(minLength, maxLength) || (minLength == null && maxLength != null) || (minLength != null && maxLength == null)) {
                return BFFSharkUtil.getSharkValueFormat(MEMBER_CARD_NUMBER_LETTER_FIXED_CHECK_FAIL,
                        minLength != null ? String.valueOf(minLength) : String.valueOf(maxLength));
            } else {
                return BFFSharkUtil.getSharkValueFormat(MEMBER_CARD_NUMBER_LETTER_RANGE_CHECK_FAIL,
                        String.valueOf(minLength), String.valueOf(maxLength));
            }
        }
        return null;
    }

    private boolean equals(Integer first, Integer second) {
        if (Objects.isNull(first) || Objects.isNull(second)) {
            return false;
        }
        return NumberUtils.compare(first, second) == 0;
    }

    protected ParamCheckResult checkContinueCommitInfo(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        if (orderCreateToken == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(orderCreateToken.getContinueTypes())) {
            return null;
        }
        if (orderCreateToken.getContinueTypes().contains(ContinueTypeConst.SINGLE_APPROVAL_FLOW)) {
            if (orderCreateRequestType.getApprovalFlowInput() == null) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_SINGLE_APPROVAL_MISS_ERROR,
                    OrderCreateErrorEnum.PARAM_VALID_SINGLE_APPROVAL_MISS_ERROR.getErrorCode().toString());
            }
        }
        // 有rc管控 缺失rc信息
        if (orderCreateToken.getContinueTypes().contains(ContinueTypeConst.RC_CONTROL)
            && QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.RC_CHECK,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            if (CollectionUtil.isEmpty(orderCreateRequestType.getRcInfos())) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.MISS_RC_LIST,
                    OrderCreateErrorEnum.MISS_RC_LIST.getErrorCode().toString());
            }
        }
        return null;
    }

    protected ParamCheckResult checkDuplexModel(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        if (orderCreateRequestType.getBookModeInfo() == null) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs() == null) {
            return null;
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        // 双拼开关 开关预定流程修改为关闭 拦截预定
        if (StringUtil.equalsIgnoreCase(orderCreateRequestType.getBookModeInfo().getBookingType(), "SHARE_ROOM")
            || StringUtil.equalsIgnoreCase(orderCreateRequestType.getBookModeInfo().getBookingType(),
            "OTHER_ORDER_ROOM")) {
            if (!accountInfo.isHotelDuplexModel()) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_DUPLEX_MODEL_CLOSE_ERROR,
                    OrderCreateErrorEnum.PARAM_VALID_DUPLEX_MODEL_CLOSE_ERROR.getErrorCode().toString());
            }
        }
        // 双拼同住 必须包含本人入住人 入住人人数<=3 房间数=1
        if (StringUtil.equalsIgnoreCase(orderCreateRequestType.getBookModeInfo().getBookingType(), "SHARE_ROOM")) {
            if (!hasSelfClientPsg(orderCreateRequestType)
                || orderCreateRequestType.getHotelBookPassengerInputs().size() > SHARE_ROOM_MAX_PSG) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_SHARE_ROOM_PSG_ERROR,
                    OrderCreateErrorEnum.PARAM_VALID_SHARE_ROOM_PSG_ERROR.getErrorCode().toString());
            }
            if (orderCreateRequestType.getHotelBookInput().getRoomQuantity() != SHARE_ROOM_MAX_ROOM) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_SHARE_ROOM_QUALITY_MISS_SELF_ERROR,
                    OrderCreateErrorEnum.PARAM_VALID_SHARE_ROOM_QUALITY_MISS_SELF_ERROR.getErrorCode().toString());
            }
        }
        return null;
    }

    private boolean hasSelfClientPsg(OrderCreateRequestType orderCreateRequestType) {
        return orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(
            p -> StringUtil.equalsIgnoreCase(p.getHotelPassengerInput().getEmployee(), "T")
                && StringUtil.equalsIgnoreCase(p.getHotelPassengerInput().getUid(),
                orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId()));
    }

    protected ParamCheckResult checkEarnPointsClientPsg(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (!StringUtil.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getPointsInfo()).map(PointsInfo::getNeedPoints).orElse(null),
            "T")) {
            return null;
        }
        String gdsType =
            Optional.ofNullable(queryCheckAvailContextResponseType.getRoomInfo()).map(BookRoomInfoEntity::getGdsType)
                .orElse(null);
        if (StringUtil.isBlank(gdsType)) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs() == null) {
            return null;
        }
        String contactInfoPhone =
            Optional.ofNullable(orderCreateRequestType.getHotelContactorInfo()).map(HotelContactorInfo::getPhoneInfo)
                .map(PhoneInfo::getPhoneNo).orElse(null);
        String contactInfoCountryCode =
            Optional.ofNullable(orderCreateRequestType.getHotelContactorInfo()).map(HotelContactorInfo::getPhoneInfo)
                .map(PhoneInfo::getCountryCode).orElse(null);
        if (StringUtil.isBlank(contactInfoPhone) || StringUtil.isBlank(contactInfoCountryCode)
            || orderCreateRequestType.getHotelBookPassengerInputs().stream().noneMatch(p ->
            StringUtil.equalsIgnoreCase(Optional.ofNullable(p.getPhoneInfo()).map(PhoneInfo::getPhoneNo).orElse(null),
                contactInfoPhone) && StringUtil.equalsIgnoreCase(p.getPhoneInfo().getCountryCode(),
                contactInfoCountryCode))) {
            // {0}需要保证联系人手机号与入住人手机号一致，请更换联系人手机号
            String friendlyMessage =
                BFFSharkUtil.getSharkValue(StringUtil.indexedFormat(SharkKeyConstant.EARN_POINTS_PHONE_ERROR, gdsType));
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_POINT_PHONE_ERROR.getErrorCode(),
                String.valueOf(OrderCreateErrorEnum.PARAM_VALID_POINT_PHONE_ERROR.getErrorCode()),
                OrderCreateErrorEnum.PARAM_VALID_POINT_PHONE_ERROR.getErrorMessage(),
                friendlyMessage);
        }
        return null;
    }

    protected ParamCheckResult checkMembershipCardNumFormat(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (StringUtil.isBlank(
            Optional.ofNullable(orderCreateRequestType.getMembershipInfo()).map(MembershipInfo::getMembershipNo)
                .orElse(null))) {
            return null;
        }
        if (isAmadues(queryCheckAvailContextResponseType)) {
            if (orderCreateRequestType.getMembershipInfo().getMembershipNo().length() > MEMBERSHIP_CARD_MAX_LENGTH) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_MEMBERSHIP_NO_ERROR,
                    String.valueOf(OrderCreateErrorEnum.PARAM_VALID_MEMBERSHIP_NO_ERROR.getErrorCode()));
            }
            char[] chars = orderCreateRequestType.getMembershipInfo().getMembershipNo().toCharArray();
            for (char c : chars) {
                if (!isLetterOrDigit(c)) {
                    return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_MEMBERSHIP_NO_ERROR,
                        String.valueOf(OrderCreateErrorEnum.PARAM_VALID_MEMBERSHIP_NO_ERROR.getErrorCode()));
                }
            }
        }
        return null;
    }

    private boolean isLetterOrDigit(char c) {
        // 0~9
        if (c >= MEMBER_PARENT_NUMBER_START && c <= MEMBER_PARENT_NUMBER_END) {
            return true;
        }
        // A~Z
        if (c >= MEMBER_PARENT_UPPERCASE_LETTERS_START && c <= MEMBER_PARENT_UPPERCASE_LETTERS_END) {
            return true;
        }
        // a~z
        if (c >= MEMBER_PARENT_LOWERCASE_LETTERS_START && c <= MEMBER_PARENT_LOWERCASE_LETTERS_END) {
            return true;
        }
        return false;
    }

    protected ParamCheckResult checkBookingForBoss(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getApprovalInput() == null || !StringUtil.equalsIgnoreCase(
            orderCreateRequestType.getApprovalInput().getEmergency(), "T")) {
            return null;
        }
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.BOOKING_FOR_BOSS_FORBID_SELF,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs() == null) {
            return null;
        }
        if (hasSelfClientPsg(orderCreateRequestType)) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_BOSS_FORBID_SELF, "checkBoss error");
        }
        return null;
    }

    protected ParamCheckResult checkTravelPolicy(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            return null;
        }
        if (!CorpPayInfoUtil.isPublic(orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        if (accountInfo == null) {
            return null;
        }
        if (!accountInfo.isTravelStandPolicy()) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(
            p -> p.getHotelPassengerInput().getRoomIndex() == null || p.getHotelPassengerInput().getRoomIndex() < 0)) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_ROOM_INDEX_EMPTY,
                OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_ROOM_INDEX_EMPTY.getErrorCode().toString());
        }
        return null;
    }

    protected ParamCheckResult checkWelfareRoomClientPsg(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (queryCheckAvailContextResponseType == null || queryCheckAvailContextResponseType.getRoomInfo() == null) {
            return null;
        }
        if (!BooleanUtils.isTrue(queryCheckAvailContextResponseType.getRoomInfo().isWelfareRoom())) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs() == null) {
            return null;
        }
        if (hasSelfClientPsg(orderCreateRequestType)) {
            return null;
        }
        return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_WELFARE_ROOM_MUST_HAS_SELF,
            OrderCreateErrorEnum.PARAM_VALID_WELFARE_ROOM_MUST_HAS_SELF.getErrorCode().toString());
    }

    protected ParamCheckResult checkOrderTripPolicyId(OrderCreateRequestType orderCreateRequestType,
                                                      SearchTripDetailResponseType searchTripDetailResponseType,
                                                      WrapperOfAccount.AccountInfo accountInfo) {
        if (!StrategyOfBookingInitUtil.isTripPolicyIdMustSameOrderPolicyId(orderCreateRequestType.getStrategyInfos())) {
            return null;
        }
        if (!accountInfo.isPolicyModel()) {
            return null;
        }
        String tripPolicyId = Optional.ofNullable(searchTripDetailResponseType)
                .map(SearchTripDetailResponseType::getBasicInfo).map(BasicInfo::getPolicyUid).orElse(null);
        if (StringUtil.isBlank(tripPolicyId)) {
            return null;
        }
        String orderPolicyId = Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput())
                .map(HotelPolicyInput::getPolicyInput).map(PolicyInput::getPolicyUid).orElse(null);
        if (StringUtil.isBlank(orderPolicyId)) {
            return null;
        }
        if (!StringUtil.equalsIgnoreCase(tripPolicyId, orderPolicyId)) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.TRIPPOLICYID_MUST_SAME_ORDERPOLICYID,
                    OrderCreateErrorEnum.TRIPPOLICYID_MUST_SAME_ORDERPOLICYID.getErrorCode().toString());
        }
        return null;
    }

    protected ParamCheckResult checkTripClientPsg(OrderCreateRequestType orderCreateRequestType,
        SearchTripDetailResponseType searchTripDetailResponseType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Offline) {
            return null;
        }
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.CLIENT_PSG_POLICY_PSG_MUST_SAME,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return null;
        }
        String policyUidTripDetail =
            Optional.ofNullable(searchTripDetailResponseType).map(SearchTripDetailResponseType::getBasicInfo)
                .map(BasicInfo::getPolicyUid).orElse(null);
        if (StringUtil.isBlank(policyUidTripDetail)) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs() == null) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs().size() > 1) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_POLICY_PSG_MUST_SAME,
                OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_POLICY_PSG_MUST_SAME.getErrorCode().toString());
        }
        if (!StringUtil.equalsIgnoreCase(policyUidTripDetail,
            orderCreateRequestType.getHotelBookPassengerInputs().get(0).getHotelPassengerInput().getUid())) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_POLICY_PSG_MUST_SAME,
                OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_POLICY_PSG_MUST_SAME.getErrorCode().toString());
        }
        return null;
    }

    protected ParamCheckResult checkPersonalAccount(OrderCreateRequestType orderCreateRequestType,
        QueryIndividualAccountResponseType queryIndividualAccountResponseType) {
        // 心程贝提交个人账户开关校验
        if (!StrategyOfBookingInitUtil.bookingWithPersonalAccount(orderCreateRequestType.getStrategyInfos())) {
            return null;
        }
        if (!PersonAccountUtil.supportPersonalAccount(queryIndividualAccountResponseType,
            orderCreateRequestType.getCorpPayInfo(),
            orderCreateRequestType.getIntegrationSoaRequestType())) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_XCB_CLOSE,
                OrderCreateErrorEnum.PARAM_VALID_XCB_CLOSE.getErrorCode().toString());
        }
        return null;
    }

    protected ParamCheckResult checkCheckAvailContextResult(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (queryCheckAvailContextResponseType == null || queryCheckAvailContextResponseType.getRoomInfo() == null) {
            return null;
        }
        // 前端传入的房间数需要反查可定的匹配上
        if (queryCheckAvailContextResponseType.getBaseInfo().getQuantity() == null) {
            return null;
        }
        if (Objects.equals(queryCheckAvailContextResponseType.getBaseInfo().getQuantity(),
            orderCreateRequestType.getHotelBookInput().getRoomQuantity())) {
            return null;
        }
        return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_ROOM_QUANTITY_ERROR,
            OrderCreateErrorEnum.PARAM_VALID_ROOM_QUANTITY_ERROR.getErrorCode().toString());
    }


    /**
     * 分摊总金额校验
     */
    protected ParamCheckResult checkCostAllocationResult(AllocationResultToken allocationResultToken) {
        if (allocationResultToken == null) {
            return null;
        }
        if (null == allocationResultToken.getTotalAmount() || MapUtils.isEmpty(
            allocationResultToken.getSettleAllocationAmount())) {
            return null;
        }
        BigDecimal compareTotalAmount = BigDecimal.ZERO;
        for (BigDecimal amount : allocationResultToken.getSettleAllocationAmount().values()) {
            if (null == amount) {
                continue;
            }
            compareTotalAmount = compareTotalAmount.add(amount);
        }
        boolean notMatch =
            allocationResultToken.getTotalAmount().compareTo(compareTotalAmount) != NumberUtils.INTEGER_ZERO;
        if (notMatch) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_COST_ALLOCATION_AMOUNT,
                OrderCreateErrorEnum.PARAM_VALID_COST_ALLOCATION_AMOUNT.getErrorCode().toString());
        }
        return null;
    }

    /**
     * 1.入住人不可为空
     * 2.海外 港澳台 amadeus英文名不可为空
     * 3.中英文名称必须任一存在
     * 4.非团队房，入住人id不可相同
     * 5.非团队房，根据qconfig配置判断人名是否可相同
     *
     * 增加6----入参总人数不可大于 房间容量×房间数  最新版已经支持throw了
     * 增加7----前选人场景单房间人数超过房间容量
     * 增加8----前选人场景入住人下有房间数>总房间数 例如：前选人选了两个人，roomIndex分别是1和2，1人一间，创单传入的roomQuantity却是1
     *
     * @param orderCreateRequestType
     * @return
     */
    protected ParamCheckResult checkClientPsg(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (orderCreateRequestType.getHotelBookPassengerInputs() == null) {
            return null;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(
            p -> StringUtil.isBlank(p.getName()) && StringUtil.isBlank(
                OrderCreateProcessorOfUtil.getEname(p, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap)))) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_EMPTY,
                OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_EMPTY.getErrorCode().toString());
        }
        // 入住人员工缺失id 非员工缺失infoid
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(p ->
            (isTrue(p.getHotelPassengerInput().getEmployee()) && StringUtil.isBlank(
                p.getHotelPassengerInput().getUid())) || !checkInfoId(p))) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_ID_EMPTY,
                OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_ID_EMPTY.getErrorCode().toString());
        }
        if (OrderCreateProcessorOfUtil.usePsgEname(orderCreateRequestType.getCityInput().getCityId(), checkAvailInfo)) {
            if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(p -> StringUtil.isBlank(
                OrderCreateProcessorOfUtil.getEname(p, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap)))) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_ENAME_EMPTY,
                    OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_ENAME_EMPTY.getErrorCode().toString());
            }
        }
        // 入住人人数不对 人数不可大于 房间容量×房间数  人数不可小于房间数 最新版已经支持throw了
        int guestPersonSize = orderCreateRequestType.getHotelBookPassengerInputs().size();
        int roomQuantity = TemplateNumberUtil.getValue(orderCreateRequestType.getHotelBookInput().getRoomQuantity());
        int guestPersonLimit = checkAvailInfo.getGuestPerson();
        if (guestPersonSize > roomQuantity * guestPersonLimit) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.GUEST_PERSON_SIZE_ERROR,
                "guestPersonSize error");
        }
        if (guestPersonSize < roomQuantity) {
            throw BusinessExceptionBuilder.createAlertException(
                OrderCreateErrorEnum.GUEST_PERSON_OR_ROOM_QUANTITY_SIZE_ERROR, "guestPersonSize or roomQuantity error");
        }
        // 入住人数超过房间容量
        if (accountInfo.isTravelStandPolicy() || accountInfo.isPolicyModel()) {
            checkGuestPerson(orderCreateRequestType, checkAvailInfo);
        }
        // 基本参数缺失校验 非团队房入住人相同
        return checkClientPsgSame(orderCreateRequestType, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap);
    }

    /**
     * 前选人场景单房间人数超过房间容量
     * 前选人场景入住人下有房间数>总房间数
     *
     * @param orderCreateRequestType
     * @param checkAvailInfo
     * @return
     */
    protected void checkGuestPerson(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        Map<Integer, List<HotelBookPassengerInput>> hotelBookPassengerInputMapByRoomIndex =
            orderCreateRequestType.getHotelBookPassengerInputs().stream()
                .filter(this::hasRoomIndex).collect(
                    Collectors.groupingByConcurrent(passenger -> passenger.getHotelPassengerInput().getRoomIndex()));
        hotelBookPassengerInputMapByRoomIndex.forEach((roomIndex, hotelBookPassengerInputs) -> {
            if (hotelBookPassengerInputs == null || hotelBookPassengerInputs.size() > checkAvailInfo.getGuestPerson()) {
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.GUEST_PERSON_SIZE_ERROR,
                    "guestPersonSize error");
            }
            if (roomIndex > orderCreateRequestType.getHotelBookInput().getRoomQuantity()) {
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.GUEST_PERSON_ROOM_INDEX_ERROR,
                    "roomIndex error");
            }
        });
    }

    private boolean hasRoomIndex(HotelBookPassengerInput hotelBookPassengerInput) {
        return hotelBookPassengerInput != null && hotelBookPassengerInput.getHotelPassengerInput() != null
            && hotelBookPassengerInput.getHotelPassengerInput().getRoomIndex() != null;
    }

    /**
     * 校验非员工id是否符合预期
     * @return
     */
    protected boolean checkInfoId(HotelBookPassengerInput hotelBookPassengerInput) {
        if (isTrue(hotelBookPassengerInput.getHotelPassengerInput().getEmployee())) {
            return true;
        }
        if (BooleanUtil.parseStr(false).equalsIgnoreCase(hotelBookPassengerInput.getSaveProfileFlag())) {
            return true;
        }
        if (StringUtil.isBlank(hotelBookPassengerInput.getHotelPassengerInput().getInfoId())) {
            return false;
        }
        return StringUtils.isNumeric(hotelBookPassengerInput.getHotelPassengerInput().getInfoId());
    }

    private boolean isAmadues(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        return StringUtil.equalsIgnoreCase(
            Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getRoomInfo)
                .map(BookRoomInfoEntity::getGdsType).orElse(null), "Amadues");
    }

    protected ParamCheckResult checkClientPsgSame(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo queryCheckAvailContextResponseType,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (orderCreateRequestType.getHotelBookPassengerInputs() == null) {
            return null;
        }
        if (StringUtil.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getTeamRoomInfo()).map(TeamRoomInfo::getTeamRoom).orElse(null),
            "T")) {
            return null;
        }
        // 入住人唯一标识
        List<String> id = new ArrayList<>();
        Set<String> idSet = new TreeSet<>();
        List<String> uidName = new ArrayList<>();
        Set<String> uidNameSet = new TreeSet<>();
        List<String> infoIdName = new ArrayList<>();
        Set<String> infoIdNameSet = new TreeSet<>();
        orderCreateRequestType.getHotelBookPassengerInputs().forEach(p -> {
            if (p == null) {
                return;
            }
            String useNameCnOrEn = OrderCreateProcessorOfUtil.getUseName(p, orderCreateRequestType.getCityInput().getCityId(),
                queryCheckAvailContextResponseType, qconfigOfCertificateInitConfig, strategyInfoMap);
            id.add(getId(p));
            idSet.add(getId(p));
            if (isTrue(p.getHotelPassengerInput().getEmployee())) {
                uidName.add(useNameCnOrEn);
                uidNameSet.add(useNameCnOrEn);
            } else {
                infoIdName.add(useNameCnOrEn);
                infoIdNameSet.add(useNameCnOrEn);
            }
        });
        if (id.size() != idSet.size()) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_SAME,
                OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_SAME.getErrorCode().toString());
        }
        // 两个重名的员工
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.ALLOW_REPEAT_NAME_OF_EMPLOYEE_EMPLOYEE,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            if (uidName.size() != uidNameSet.size()) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_SAME_UID_TO_UID,
                    OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_SAME_UID_TO_UID.getErrorCode().toString());
            }
        }
        // 一个员工和一个非员工重名
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.ALLOW_REPEAT_NAME_OF_EMPLOYEE_NONEMPLOYEE,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            if (CollectionUtil.isNotEmpty(uidNameSet) && CollectionUtil.isNotEmpty(infoIdNameSet) && (
                uidNameSet.stream().anyMatch(infoIdNameSet::contains) || infoIdNameSet.stream()
                    .anyMatch(uidNameSet::contains))) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_SAME_INFOID_TO_UID,
                    OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_SAME_INFOID_TO_UID.getErrorCode().toString());
            }
        }
        // 两个重名的非员工
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.ALLOW_REPEAT_NAME_OF_NONEMPLOYEE_NONEMPLOYEE,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            if (infoIdName.size() != infoIdNameSet.size()) {
                return new ParamCheckResult(false,
                    OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_SAME_INFOID_TO_INFOID,
                    OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_NAME_SAME_INFOID_TO_INFOID.getErrorCode().toString());
            }
        }
        return null;
    }

    private boolean isTrue(String str) {
        return StringUtil.equalsIgnoreCase(str, "T");
    }

    private String getId(HotelBookPassengerInput p) {
        if (StringUtil.isNotBlank(p.getHotelPassengerInput().getUid())) {
            return p.getHotelPassengerInput().getUid();
        }
        if (StringUtil.isNotBlank(p.getHotelPassengerInput().getInfoId())) {
            return p.getHotelPassengerInput().getInfoId();
        }
        if (StringUtil.isNotBlank(p.getHotelPassengerInput().getTemporaryId()) && BooleanUtil.parseStr(false)
            .equalsIgnoreCase(p.getSaveProfileFlag())) {
            return p.getHotelPassengerInput().getTemporaryId();
        }
        return null;
    }

    protected ParamCheckResult checkThirdPartyAgreementPsg(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        // 异常入参
        if (orderCreateRequestType == null || queryCheckAvailContextResponseType == null) {
            return null;
        }

        // 不是因私不用校验
        if (!CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }

        // 非三方协议酒店不用校验
        if (!isThirdPartyAgreementRoom(queryCheckAvailContextResponseType.getRoomInfo())) {
            return null;
        }

        // 查询qconfig里是否有配置对应公司, 没配置就不用校验
        String corpId = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getCorpId).orElse(null);
        if (!QConfigOfCustomConfig.isSupport(CHECK_THIRD_PARTY_AGREEMENT_PSG_QCONFIG_KEY, corpId)) {
            return null;
        }

        // 校验入住人是否含卡主本人
        List<HotelBookPassengerInput> hotelPassengerInfos = orderCreateRequestType.getHotelBookPassengerInputs();
        if (hotelPassengerInfos == null) {
            return null;
        }
        if (!CollectionUtil.isEmpty(hotelPassengerInfos)) {
            String uid = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null);
            for (HotelBookPassengerInput passengerInput : hotelPassengerInfos) {
                if (passengerInput == null) {
                    continue;
                }
                // 入住人包含本人, 通过校验
                if (StringUtil.equals(passengerInput.getHotelPassengerInput().getUid(), uid)) {
                    return null;
                }
            }
        }

        // 入住人不含卡主本人, 弹框拦截：提示“预订公司协议价，入住人需包含本人，请更换其他非协议房型或修改入住人”
        return new ParamCheckResult(false, OrderCreateErrorEnum.PARAM_VALID_THIRD_PARTY_AGREEMENT_PSG_ERROR,
            OrderCreateErrorEnum.PARAM_VALID_THIRD_PARTY_AGREEMENT_PSG_ERROR.getErrorCode().toString());
    }

    /**
     * 判断酒店是否是三方协议
     *
     * @param roomInfo
     * @return
     */
    private boolean isThirdPartyAgreementRoom(BookRoomInfoEntity roomInfo) {
        if (roomInfo == null) {
            return false;
        }
        // 是协议酒店 并且 不是两方协议
        return "C".equals(roomInfo.getRoomType()) && "NONE".equals(roomInfo.getTmcPriceType());
    }
}
